import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// Rutas que no requieren autenticación
const publicRoutes = ["/", "/login", "/auth/callback"];

// Rutas de recursos estáticos que siempre deben ser accesibles
const staticRoutes = [
  "/_next",
  "/favicon.ico",
  "/robots.txt",
  "/manifest.json",
  "/images",
  "/icons",
  "/fonts",
];

// Rutas que requieren autenticación
const privateRoutes = [
  "/homeworks",
  "/evaluations",
  "/courses",
  "/criterias",
  "/indicators",
  "/students",
  "/subjects",
  "/dashboard",
  "/admin",
  "/profile",
  "/settings",
];

export async function middleware(request: NextRequest) {
  // Obtener la ruta actual
  const { pathname } = request.nextUrl;

  // Verificar si es una ruta de recursos estáticos
  const isStaticRoute = staticRoutes.some((route) =>
    pathname.startsWith(route)
  );
  if (isStaticRoute) {
    return NextResponse.next();
  }

  // Verificar si es una ruta pública
  const isPublicRoute = publicRoutes.includes(pathname);
  if (isPublicRoute) {
    return NextResponse.next();
  }

  // Para rutas de API
  if (pathname.startsWith("/api/")) {
    return NextResponse.next();
  }

  // Verificar si es una ruta privada
  const isPrivateRoute = privateRoutes.some(
    (route) => pathname === route || pathname.startsWith(`${route}/`)
  );

  // Si es una ruta privada, verificar autenticación en el cliente
  if (isPrivateRoute) {
    // Permitir el acceso y dejar que el cliente maneje la autenticación
    return NextResponse.next();
  }

  // Para cualquier otra ruta, permitir el acceso
  return NextResponse.next();
}

// Configurar las rutas que deben ser procesadas por el middleware
export const config = {
  matcher: [
    /*
     * Coincide con todas las rutas excepto:
     * - _next/static (archivos estáticos de Next.js)
     * - _next/image (optimización de imágenes)
     * - favicon.ico, robots.txt, etc.
     */
    "/((?!_next/static|_next/image|favicon.ico|robots.txt).*)",
  ],
};
