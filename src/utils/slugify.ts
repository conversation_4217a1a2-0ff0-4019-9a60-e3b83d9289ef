export function slugify(text: string): string {
  const specialChars: { [key: string]: string } = {
    á: "a",
    à: "a",
    ã: "a",
    â: "a",
    ä: "a",
    é: "e",
    è: "e",
    ê: "e",
    ë: "e",
    í: "i",
    ì: "i",
    î: "i",
    ï: "i",
    ó: "o",
    ò: "o",
    õ: "o",
    ô: "o",
    ö: "o",
    ú: "u",
    ù: "u",
    û: "u",
    ü: "u",
    ý: "y",
    ÿ: "y",
    ñ: "n",
    Á: "A",
    À: "A",
    Ã: "A",
    Â: "A",
    Ä: "A",
    É: "E",
    È: "E",
    Ê: "E",
    Ë: "E",
    Í: "I",
    Ì: "I",
    Î: "I",
    Ï: "I",
    Ó: "O",
    Ò: "O",
    Õ: "O",
    Ô: "O",
    Ö: "O",
    Ú: "U",
    Ù: "U",
    Û: "U",
    Ü: "U",
    Ý: "Y",
    Ñ: "N",
  };

  return text
    .toString()
    .toLowerCase()
    .split("")
    .map((char) => specialChars[char] || char)
    .join("")
    .trim()
    .replace(/\s+/g, "-") // Reemplazar espacios con guiones
    .replace(/[^\w\-]+/g, "") // Eliminar caracteres especiales
    .replace(/\-\-+/g, "-") // Reemplazar múltiples guiones con uno solo
    .replace(/^-+|-+$/g, ""); // Eliminar guiones al inicio y final
}
