import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { type NextRequest, NextResponse } from "next/server";

export const createClient = (request: NextRequest) => {
  // Crear una respuesta inicial de Next.js
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  // Imprimir cookies para depuración
  console.log(
    "Middleware - Cookies disponibles:",
    request.cookies.getAll().map((c) => c.name)
  );

  // Crear cliente de Supabase para el middleware
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          const cookie = request.cookies.get(name);
          const value = cookie?.value;
          console.log(
            `Middleware - Cookie ${name}:`,
            value ? "presente" : "ausente"
          );
          return value;
        },
        set(name: string, value: string, options: CookieOptions) {
          console.log(`Middleware - Estableciendo cookie ${name}`);
          // Si la cookie debe ser eliminada, establecer su valor como vacío
          if (value === "") {
            request.cookies.delete(name);
            response = NextResponse.next({
              request: {
                headers: request.headers,
              },
            });
            response.cookies.delete(name);
          } else {
            // Establecer la cookie tanto en la solicitud como en la respuesta
            try {
              request.cookies.set({
                name,
                value,
                ...options,
              });
              response = NextResponse.next({
                request: {
                  headers: request.headers,
                },
              });
              response.cookies.set({
                name,
                value,
                ...options,
              });
            } catch (error) {
              console.error(
                `Middleware - Error al establecer cookie ${name}:`,
                error
              );
            }
          }
        },
        remove(name: string, options: CookieOptions) {
          console.log(`Middleware - Eliminando cookie ${name}`);
          // Eliminar la cookie tanto de la solicitud como de la respuesta
          try {
            request.cookies.delete(name);
            response = NextResponse.next({
              request: {
                headers: request.headers,
              },
            });
            response.cookies.delete(name);
          } catch (error) {
            console.error(
              `Middleware - Error al eliminar cookie ${name}:`,
              error
            );
          }
        },
      },
    }
  );

  return { supabase, response };
};
