"use client";

import { useEffect, useState } from "react";
import { useDomain } from "@/components/Context";
import styles from "./students.module.css";
import Link from "next/link";
import { HeroSection, HeroVariant } from "@/components/HeroSection";
import { supabase } from "@/utils/supabase/client";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  Snackbar,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";

interface Student {
  id: string;
  first_name: string;
  last_name1: string;
  last_name2?: string;
  is_pi: boolean;
  slug: string;
  created_at: string;
  updated_at: string;
  course?: {
    id: string;
    title: string;
    description: string;
    slug: string;
  };
}

interface Course {
  id: string;
  title: string;
  description: string;
  slug: string;
}

export default function StudentsPage() {
  const [students, setStudents] = useState<Student[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [newStudent, setNewStudent] = useState({
    first_name: "",
    last_name1: "",
    last_name2: "",
    is_pi: false,
    course_id: "",
  });
  const [openDialog, setOpenDialog] = useState(false);
  const [studentToDelete, setStudentToDelete] = useState<Student | null>(null);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: "success" | "error" | "info";
  }>({
    open: false,
    message: "",
    severity: "info",
  });

  const { domain } = useDomain();

  const fetchStudents = async () => {
    try {
      const studentsList = await domain.getStudentsListUseCase.execute();
      setStudents(studentsList);
    } catch (error) {
      console.error("Error al obtener la lista de alumnos:", error);
      setSnackbar({
        open: true,
        message: "Error al obtener la lista de alumnos",
        severity: "error",
      });
    }
  };

  const fetchCourses = async () => {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session?.user?.id) {
        throw new Error("No hay usuario autenticado");
      }

      const coursesList = await domain.getCoursesListUseCase.execute(
        session.user.id
      );
      setCourses(coursesList);
    } catch (error) {
      console.error("Error al obtener la lista de cursos:", error);
      setSnackbar({
        open: true,
        message: "Error al obtener la lista de cursos",
        severity: "error",
      });
    }
  };

  useEffect(() => {
    fetchStudents();
    fetchCourses();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (courses.length === 0) {
      setSnackbar({
        open: true,
        message: "Debes crear un curso primero",
        severity: "error",
      });
      return;
    }

    if (!newStudent.course_id) {
      setSnackbar({
        open: true,
        message: "Debes seleccionar un curso",
        severity: "error",
      });
      return;
    }

    try {
      const result = await domain.addStudentUseCase.execute(newStudent);

      if (!result) {
        throw new Error("No se pudo añadir el alumno");
      }

      setNewStudent({
        first_name: "",
        last_name1: "",
        last_name2: "",
        is_pi: false,
        course_id: "",
      });
      fetchStudents();
      setSnackbar({
        open: true,
        message: "Alumno añadido correctamente",
        severity: "success",
      });
    } catch (error) {
      console.error("Error al añadir el alumno:", error);
      setSnackbar({
        open: true,
        message:
          error instanceof Error
            ? `Error: ${error.message}`
            : "Error al añadir el alumno",
        severity: "error",
      });
    }
  };

  const handleDeleteClick = (student: Student) => {
    setStudentToDelete(student);
    setOpenDialog(true);
  };

  const handleConfirmDelete = async () => {
    if (studentToDelete) {
      try {
        await domain.deleteStudentUseCase.execute({
          studentId: studentToDelete.id,
        });
        setOpenDialog(false);
        setStudentToDelete(null);
        fetchStudents();
        setSnackbar({
          open: true,
          message: `Alumno "${studentToDelete.first_name} ${studentToDelete.last_name1}" eliminado correctamente`,
          severity: "success",
        });
      } catch (error) {
        console.error("Error al eliminar el alumno:", error);
        setSnackbar({
          open: true,
          message: "Error al eliminar el alumno",
          severity: "error",
        });
      }
    }
  };

  const handleCancelDelete = () => {
    setOpenDialog(false);
    setStudentToDelete(null);
    setSnackbar({
      open: true,
      message: "Operación cancelada",
      severity: "info",
    });
  };

  const handleCloseSnackbar = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  return (
    <div className={styles.container}>
      <HeroSection
        title="Gestión de Alumnos"
        description="Administra la información de tus estudiantes. Registra nuevos alumnos, asigna cursos y realiza un seguimiento de su progreso académico. Los alumnos son el centro de tu proceso educativo."
        ctaText="Registrar Nuevo Alumno"
        ctaUrl="#form-section"
        variant={HeroVariant.STUDENTS}
      />

      <div className={styles.content}>
        <div className={styles.listSection}>
          <h2 className={styles.subtitle}>Lista de Alumnos</h2>
          <div className={styles.studentsList}>
            {students.map((student) => (
              <div key={student.id} className={styles.studentCard}>
                <Link
                  href={`/students/${student.slug}`}
                  className={styles.studentInfo}
                >
                  <h3 className={styles.studentName}>
                    {student.first_name} {student.last_name1}
                    {student.last_name2 && ` ${student.last_name2}`}
                  </h3>
                  <div className={styles.studentDetails}>
                    <span
                      className={
                        student.is_pi ? styles.piBadge : styles.noPiBadge
                      }
                    >
                      {student.is_pi ? "PI" : "No PI"}
                    </span>
                    {student.course && (
                      <span className={styles.courseBadge}>
                        {student.course.title}
                      </span>
                    )}
                  </div>
                </Link>
                <div className={styles.actions}>
                  <button
                    className={styles.deleteButton}
                    onClick={() => handleDeleteClick(student)}
                    aria-label="Eliminar alumno"
                  >
                    ×
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className={styles.formSection} id="form-section">
          <h2 className={styles.subtitle}>Registrar Nuevo Alumno</h2>
          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.formGroup}>
              <label htmlFor="first_name">Nombre</label>
              <input
                type="text"
                id="first_name"
                value={newStudent.first_name}
                onChange={(e) =>
                  setNewStudent({ ...newStudent, first_name: e.target.value })
                }
                required
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="last_name1">Primer Apellido</label>
              <input
                type="text"
                id="last_name1"
                value={newStudent.last_name1}
                onChange={(e) =>
                  setNewStudent({ ...newStudent, last_name1: e.target.value })
                }
                required
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="last_name2">Segundo Apellido (opcional)</label>
              <input
                type="text"
                id="last_name2"
                value={newStudent.last_name2}
                onChange={(e) =>
                  setNewStudent({ ...newStudent, last_name2: e.target.value })
                }
              />
            </div>
            <div className={styles.formGroup}>
              <label className={styles.checkboxLabel}>
                <input
                  type="checkbox"
                  checked={newStudent.is_pi}
                  onChange={(e) =>
                    setNewStudent({ ...newStudent, is_pi: e.target.checked })
                  }
                />
                Es PI
              </label>
            </div>
            <div className={styles.formGroup}>
              <FormControl fullWidth required>
                <InputLabel id="course-select-label">Curso</InputLabel>
                <Select
                  labelId="course-select-label"
                  id="course-select"
                  value={newStudent.course_id}
                  label="Curso"
                  onChange={(e) =>
                    setNewStudent({ ...newStudent, course_id: e.target.value })
                  }
                >
                  {courses.map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </div>
            <button type="submit" className={styles.submitButton}>
              Registrar Alumno
            </button>
          </form>
        </div>
      </div>

      <Dialog open={openDialog} onClose={handleCancelDelete}>
        <DialogTitle>Confirmar Eliminación</DialogTitle>
        <DialogContent>
          <DialogContentText>
            ¿Estás seguro de que deseas eliminar al alumno{" "}
            {studentToDelete
              ? `${studentToDelete.first_name} ${studentToDelete.last_name1}`
              : ""}
            ? Esta acción no se puede deshacer.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete}>Cancelar</Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            Eliminar
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
}
