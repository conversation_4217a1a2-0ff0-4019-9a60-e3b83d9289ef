.container {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.backButton {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 0;
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  transition: color 0.2s ease;
}

.backButton:hover {
  color: #2563eb;
}

.formCard {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header {
  margin-bottom: 2rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--title-color);
}

.input {
  padding: 0.75rem;
  border: 1px solid var(--outline-color);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.checkboxGroup {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 4px;
  border: 1px solid var(--outline-color);
  cursor: pointer;
}

.coursesList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 200px;
  overflow-y: auto;
  padding: 0.5rem;
  border: 1px solid var(--outline-color);
  border-radius: 6px;
}

.courseCheckbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.courseCheckbox:hover {
  background-color: var(--bg-primary);
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.submitButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submitButton:hover {
  background-color: #2563eb;
}

.error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .formCard {
    padding: 1.5rem;
  }
}
