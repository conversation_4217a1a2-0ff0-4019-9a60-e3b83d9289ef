"use client";

import { useEffect, useState } from "react";
import { useDomain } from "@/components/Context";
import { useParams, useRouter } from "next/navigation";
import styles from "./edit-student.module.css";
import { Alert, Snackbar } from "@mui/material";

interface Course {
  id: string;
  title: string;
  slug: string;
}

interface Student {
  id: string;
  first_name: string;
  last_name1: string;
  last_name2?: string;
  is_pi: boolean;
  slug: string;
  courses?: {
    course: Course;
  }[];
}

export default function EditStudentPage() {
  const [student, setStudent] = useState<Student | null>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState({
    first_name: "",
    last_name1: "",
    last_name2: "",
    is_pi: false,
  });
  const [selectedCourses, setSelectedCourses] = useState<string[]>([]);

  const params = useParams();
  const router = useRouter();
  const { domain } = useDomain();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const slug = params.slug as string;

        // Obtener datos del alumno
        const studentData = await domain.getStudentBySlugUseCase.execute({
          slug,
        });
        if (!studentData) {
          setError("No se pudo encontrar el alumno");
          return;
        }

        // Obtener lista de cursos
        const coursesData = await domain.getCoursesListUseCase.execute();

        setStudent(studentData);
        setCourses(coursesData);
        setFormData({
          first_name: studentData.first_name,
          last_name1: studentData.last_name1,
          last_name2: studentData.last_name2 || "",
          is_pi: studentData.is_pi,
        });

        // Establecer cursos seleccionados
        if (studentData.courses) {
          setSelectedCourses(
            studentData.courses.map((c: { course: Course }) => c.course.id)
          );
        }
      } catch (error) {
        console.error("Error al cargar los datos:", error);
        setError(
          error instanceof Error ? error.message : "Error al cargar los datos"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.slug, domain]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleCourseChange = (courseId: string) => {
    setSelectedCourses((prev) => {
      if (prev.includes(courseId)) {
        return prev.filter((id) => id !== courseId);
      } else {
        return [...prev, courseId];
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!student) return;

    try {
      // Actualizar datos del alumno
      await domain.updateStudentUseCase.execute({
        id: student.id,
        ...formData,
      });

      // Actualizar asignaciones de cursos
      const currentCourseIds = student.courses?.map((c) => c.course.id) || [];

      // Cursos a añadir
      const coursesToAdd = selectedCourses.filter(
        (courseId) => !currentCourseIds.includes(courseId)
      );

      // Cursos a eliminar
      const coursesToRemove = currentCourseIds.filter(
        (courseId) => !selectedCourses.includes(courseId)
      );

      // Añadir nuevos cursos
      for (const courseId of coursesToAdd) {
        await domain.assignStudentToCourseUseCase.execute({
          studentId: student.id,
          courseId,
        });
      }

      // Eliminar cursos no seleccionados
      for (const courseId of coursesToRemove) {
        await domain.assignStudentToCourseUseCase.execute({
          studentId: student.id,
          courseId,
          remove: true,
        });
      }

      setSuccess(true);
      setTimeout(() => {
        router.push(`/students/${student.slug}`);
      }, 1500);
    } catch (error) {
      console.error("Error al actualizar el alumno:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Error al actualizar los datos del alumno"
      );
    }
  };

  const handleGoBack = () => {
    if (student) {
      router.push(`/students/${student.slug}`);
    }
  };

  if (loading) {
    return <div>Cargando...</div>;
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.errorContainer}>
          <h2>Error</h2>
          <p>{error}</p>
          <button onClick={handleGoBack} className={styles.backButton}>
            Volver
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <button onClick={handleGoBack} className={styles.backButton}>
        ← Volver al detalle
      </button>

      <div className={styles.formCard}>
        <div className={styles.header}>
          <h1 className={styles.title}>Editar alumno</h1>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="first_name" className={styles.label}>
              Nombre
            </label>
            <input
              type="text"
              id="first_name"
              name="first_name"
              value={formData.first_name}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="last_name1" className={styles.label}>
              Primer apellido
            </label>
            <input
              type="text"
              id="last_name1"
              name="last_name1"
              value={formData.last_name1}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="last_name2" className={styles.label}>
              Segundo apellido
            </label>
            <input
              type="text"
              id="last_name2"
              name="last_name2"
              value={formData.last_name2}
              onChange={handleChange}
              className={styles.input}
            />
          </div>

          <div className={styles.checkboxGroup}>
            <input
              type="checkbox"
              id="is_pi"
              name="is_pi"
              checked={formData.is_pi}
              onChange={handleChange}
              className={styles.checkbox}
            />
            <label htmlFor="is_pi" className={styles.label}>
              Es alumno PI
            </label>
          </div>

          <div className={styles.formGroup}>
            <label className={styles.label}>Cursos asignados</label>
            <div className={styles.coursesList}>
              {courses.map((course) => (
                <div key={course.id} className={styles.courseCheckbox}>
                  <input
                    type="checkbox"
                    id={`course-${course.id}`}
                    checked={selectedCourses.includes(course.id)}
                    onChange={() => handleCourseChange(course.id)}
                    className={styles.checkbox}
                  />
                  <label
                    htmlFor={`course-${course.id}`}
                    className={styles.label}
                  >
                    {course.title}
                  </label>
                </div>
              ))}
            </div>
          </div>

          <div className={styles.formActions}>
            <button type="submit" className={styles.submitButton}>
              Guardar cambios
            </button>
          </div>
        </form>
      </div>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={() => setError(null)}
          severity="error"
          sx={{ width: "100%" }}
        >
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={success}
        autoHideDuration={6000}
        onClose={() => setSuccess(false)}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={() => setSuccess(false)}
          severity="success"
          sx={{ width: "100%" }}
        >
          Alumno actualizado correctamente
        </Alert>
      </Snackbar>
    </div>
  );
}
