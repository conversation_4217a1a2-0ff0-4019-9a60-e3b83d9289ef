.container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.backButton {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 0;
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  transition: color 0.2s ease;
}

.backButton:hover {
  color: #2563eb;
}

.loadingContainer {
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.errorContainer {
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.errorContainer h2 {
  color: #ef4444;
  margin-bottom: 1rem;
}

.errorContainer p {
  color: #64748b;
  margin-bottom: 1.5rem;
}

.actionButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.actionButton:hover {
  background-color: #2563eb;
}

.studentDetail {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--outline-color);
}

.headerInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--title-color);
  margin: 0;
}

.piBadge {
  display: inline-block;
  background-color: #f1f5f9;
  color: #64748b;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  white-space: nowrap;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

.editButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.editButton:hover {
  background-color: #2563eb;
}

.deleteButton {
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.deleteButton:hover {
  background-color: #dc2626;
}

.criteriaAveragesSummary {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--outline-color);
}

.summaryTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 1.5rem;
}

.criteriaAveragesList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.criteriaAverageItem {
  background-color: var(--bg-secondary);
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.criteriaAverageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.criteriaName {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
}

.criteriaAverageValue {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  border-radius: 4px;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.criteriaAverageDescription {
  font-size: 0.95rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

.homeworksSection {
  margin-top: 2rem;
}

.homeworksHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.homeworksTitle {
  font-size: 1.5rem;
  color: var(--title-color);
  margin: 0;
}

.homeworksList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.homeworkItem {
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  background-color: #f1f5f9;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  margin-bottom: 1rem;
}

.homeworkItem:hover {
  background-color: #e2e8f0;
}

.homeworkHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  width: 100%;
}

.homeworkTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
  flex: 1;
}

.homeworkSubject {
  display: inline-block;
  background-color: #e2e8f0;
  color: #64748b;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  margin-right: 1rem;
}

.homeworkGrade {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

.homeworkActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
  width: 100%;
}

/* Mantener los estilos antiguos para compatibilidad */
.subjectsSection {
  margin-top: 2rem;
}

.subjectsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.subjectsTitle {
  font-size: 1.5rem;
  color: var(--title-color);
  margin: 0;
}

.subjectsList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.subjectItem {
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  background-color: #f1f5f9;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  margin-bottom: 1rem;
}

.subjectItem:hover {
  background-color: #e2e8f0;
}

.subjectHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  width: 100%;
}

.subjectGrade {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

.subjectActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
  width: 100%;
}

.qualitativeEvaluation {
  background-color: white;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.evaluationTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0 0 1rem 0;
}

.indicatorsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.indicatorItem {
  border-left: 3px solid #e2e8f0;
  padding-left: 1rem;
}

.indicatorHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.indicatorTitle {
  font-weight: 500;
  color: var(--title-color);
}

.indicatorLevel {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
}

.indicatorDescription {
  color: #64748b;
  font-size: 0.875rem;
}

/* Estilos para el listado detallado de indicadores */
.indicatorsDetailSection {
  background-color: white;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  width: 100%;
}

.indicatorsDetailList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.indicatorDetailItem {
  border-left: 3px solid #e2e8f0;
  padding-left: 1rem;
  width: 100%;
  margin-bottom: 1.5rem;
}

.indicatorDetailHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.indicatorDetailTitle {
  font-weight: 700;
  color: var(--title-color);
  font-size: 1.125rem;
}

.indicatorDetailLevel {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  color: var(--title-color);
  font-weight: 500;
  font-size: 0.875rem;
}

.indicatorDetailCriteria {
  color: #94a3b8;
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  margin-top: 0.5rem;
}

.indicatorDetailDescription {
  color: #334155;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  line-height: 1.5;
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-family: inherit;
  position: relative;
  font-weight: normal;
}

.indicatorDetailDescriptionNoInteractive {
  color: #334155;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  line-height: 1.5;
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.75rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  border: none;
  font-family: inherit;
  position: relative;
  font-weight: normal;
}

.indicatorDetailDescription:hover {
  opacity: 0.9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.descriptionContent {
  flex: 1;
}

.copyButton {
  background: #3b82f6;
  border: none;
  color: white;
  cursor: pointer;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  margin-left: 0.5rem;
}

.copyButton:hover {
  background-color: #2563eb;
}

.copyIcon {
  margin-right: 0.25rem;
  font-size: 1rem;
}

.criteriaLabel {
  color: #94a3b8;
  font-size: 0.75rem;
  font-weight: 500;
}

.noEvaluations {
  text-align: center;
  padding: 2rem;
  background-color: white;
  border-radius: 6px;
  color: #64748b;
}

.subjectName {
  flex: 1;
  font-size: 1rem;
  color: var(--title-color);
  margin: 0;
}

.courseName {
  color: #64748b;
  font-size: 0.875rem;
  min-width: 200px;
}

.accessButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.accessButton:hover {
  background-color: #2563eb;
}

.emptyMessage {
  color: #64748b;
  text-align: center;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .subjectItem,
  .homeworkItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .courseName {
    min-width: auto;
  }

  .homeworkHeader {
    flex-direction: column;
    align-items: flex-start;
  }

  .homeworkSubject {
    margin-top: 0.5rem;
    margin-right: 0;
  }

  .homeworkGrade {
    margin-top: 0.5rem;
  }
}

.courseGroup {
  margin-bottom: 2rem;
}

.courseGroupTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0 0 1rem 0;
  padding: 0 1rem;
}

.courseGroup .subjectItem {
  margin-bottom: 0.5rem;
}

.courseGroup .subjectItem:last-child {
  margin-bottom: 0;
}
