.container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--title-color);
  margin-bottom: 2rem;
  text-align: center;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.formSection {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 1.5rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  font-weight: 500;
  color: var(--title-color);
}

.formGroup input[type="text"] {
  padding: 0.75rem;
  border: 1px solid var(--outline-color);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.formGroup input[type="text"]:focus {
  outline: none;
  border-color: #3b82f6;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkboxLabel input[type="checkbox"] {
  width: 1.2rem;
  height: 1.2rem;
  cursor: pointer;
}

.submitButton {
  background: linear-gradient(135deg, var(--primary) 0%, #38bdf8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(29, 136, 175, 0.2);
}

.submitButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(29, 136, 175, 0.3);
}

.listSection {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.studentsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.studentCard {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 8px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.studentCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.studentInfo {
  text-decoration: none;
  color: inherit;
  flex: 1;
}

.studentName {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
}

.actions {
  display: flex;
  align-items: center;
  margin-left: 1rem;
}

.deleteButton {
  background: none;
  border: none;
  color: #64748b;
  font-size: 1.5rem;
  font-weight: 300;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  line-height: 1;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.deleteButton:hover {
  color: #ef4444;
  background-color: #fee2e2;
}

.piBadge {
  background-color: #22c55e;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-block;
}

.noPiBadge {
  background-color: #e2e8f0;
  color: #64748b;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-block;
}

.studentDetails {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-top: 4px;
}

.courseBadge {
  background-color: #e0f2fe;
  color: #0369a1;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .content {
    grid-template-columns: 1fr;
  }

  .container {
    padding: 1rem;
  }

  .title {
    font-size: 2rem;
  }
}
