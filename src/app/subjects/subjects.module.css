/* Container principal */
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
  padding: 2rem;
}

/* Hero Section */
.heroSection {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.heroContent {
  flex: 1;
  max-width: 600px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--title-color);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.heroImageContainer {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.heroImage {
  max-width: 100%;
  height: auto;
}

/* Filters Section */
.filtersSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem 1.5rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.searchContainer {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.95rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(29, 136, 175, 0.1);
}

.searchInput::placeholder {
  color: var(--text-secondary);
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

/* Create Button */
.createButton {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--primary) 0%, #38bdf8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(29, 136, 175, 0.2);
}

.createButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(29, 136, 175, 0.3);
}

/* Group Toggle */
.groupToggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: var(--bg-primary);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.groupToggle label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--title-color);
  cursor: pointer;
  user-select: none;
}

.groupToggle input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.groupToggle .toggle {
  position: relative;
  width: 44px;
  height: 24px;
  background-color: var(--border-color);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.groupToggle .toggle:before {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  background-color: var(--bg-primary);
  transition: transform 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.groupToggle input[type="checkbox"]:checked + .toggle {
  background-color: var(--primary);
}

.groupToggle input[type="checkbox"]:checked + .toggle:before {
  transform: translateX(20px);
}

/* Subjects Section */
.subjectsSection {
  margin-bottom: 3rem;
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.emptyStateIcon {
  margin-bottom: 1.5rem;
  opacity: 0.6;
}

.emptyMessage {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 0.5rem;
}

.emptyDescription {
  color: var(--text-secondary);
  max-width: 400px;
  line-height: 1.5;
}

/* Grouped List */
.groupedList {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.courseGroup {
  background-color: var(--bg-primary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.courseGroupTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
  padding: 1.25rem 1.5rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

/* Subjects List */
.subjectsList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.subjectCard {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1.5rem;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  height: 100%;
}

.subjectCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.subjectInfo {
  margin-bottom: 1.5rem;
}

.subjectName {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0 0 0.5rem 0;
}

.courseName {
  color: var(--text-secondary);
  font-size: 0.875rem;
  display: block;
}

.accessButton {
  background: linear-gradient(135deg, var(--primary) 0%, #38bdf8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  box-shadow: 0 2px 4px rgba(29, 136, 175, 0.2);
}

.accessButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(29, 136, 175, 0.3);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .heroSection {
    flex-direction: column;
    text-align: center;
    padding: 2rem 1.5rem;
  }

  .heroContent {
    max-width: 100%;
    margin-bottom: 2rem;
  }

  .filtersSection {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .searchContainer {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .title {
    font-size: 2rem;
  }

  .description {
    font-size: 1rem;
  }

  .subjectsList {
    grid-template-columns: 1fr;
  }

  .courseGroup .subjectsList {
    padding: 1rem;
  }
}
