"use client";

import { useEffect, useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import styles from "./subjects.module.css";
import { useDomain } from "@/components/Context";
import { SearchIcon, EmptyImageIcon } from "@/components/icons";
import { HeroSection, HeroVariant } from "@/components/HeroSection";

interface Subject {
  id: string;
  name: string;
  slug: string;
  course: {
    id: string;
    title: string;
  };
}

interface GroupedSubject {
  courseTitle: string;
  subjects: Subject[];
}

export default function SubjectsPage() {
  const router = useRouter();
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [groupByCourse, setGroupByCourse] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const { domain } = useDomain();

  useEffect(() => {
    const fetchSubjects = async () => {
      try {
        const subjectsList = await domain.getSubjectsListUseCase.execute();
        setSubjects(subjectsList);
      } catch (err) {
        console.error("Error fetching subjects:", err);
        setError("Error al cargar la lista de asignaturas");
      } finally {
        setLoading(false);
      }
    };

    fetchSubjects();
  }, []);

  const filteredSubjects = useMemo(() => {
    return subjects.filter(
      (subject) =>
        subject.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        subject.course.title.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [subjects, searchTerm]);

  const groupedSubjects = useMemo(() => {
    if (!groupByCourse) return filteredSubjects;

    const groups = filteredSubjects.reduce((acc, subject) => {
      const courseTitle = subject.course.title;
      if (!acc[courseTitle]) {
        acc[courseTitle] = [];
      }
      acc[courseTitle].push(subject);
      return acc;
    }, {} as Record<string, Subject[]>);

    return Object.entries(groups).map(([courseTitle, subjects]) => ({
      courseTitle,
      subjects,
    }));
  }, [filteredSubjects, groupByCourse]) as Subject[] | GroupedSubject[];

  // La función handleAddSubject ya no es necesaria porque el componente HeroSection maneja la navegación

  const handleAccessSubject = (slug: string) => {
    router.push(`/subjects/${slug}`);
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.subjectsDetail}>
          <p>Cargando...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.subjectsDetail}>
          <p className={styles.error}>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <HeroSection
        title="Asignaturas"
        description="Las asignaturas son los componentes fundamentales de cada curso. En ellas se definen los criterios de evaluación, indicadores y trabajos que permitirán evaluar el desempeño de los estudiantes de manera estructurada."
        ctaText="+ Añadir asignatura"
        ctaUrl="/subjects/new"
        variant={HeroVariant.SUBJECTS}
      />

      <div className={styles.filtersSection}>
        <div className={styles.searchContainer}>
          <input
            type="text"
            placeholder="Buscar asignatura o curso..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={styles.searchInput}
          />
          <SearchIcon
            className={styles.searchIcon}
            style={{ color: "var(--text-secondary)" }}
          />
        </div>
        <div className={styles.groupToggle}>
          <label htmlFor="groupToggle">Agrupar por curso</label>
          <input
            id="groupToggle"
            type="checkbox"
            checked={groupByCourse}
            onChange={(e) => setGroupByCourse(e.target.checked)}
          />
          <div
            className={styles.toggle}
            role="switch"
            aria-checked={groupByCourse}
            tabIndex={0}
            onClick={() => setGroupByCourse(!groupByCourse)}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                setGroupByCourse(!groupByCourse);
              }
            }}
          />
        </div>
      </div>

      <div className={styles.subjectsSection}>
        {(() => {
          // Si no hay asignaturas, mostrar estado vacío
          if (groupedSubjects.length === 0) {
            return (
              <div className={styles.emptyState}>
                <div className={styles.emptyStateIcon}>
                  <EmptyImageIcon style={{ color: "var(--text-secondary)" }} />
                </div>
                <p className={styles.emptyMessage}>
                  {searchTerm
                    ? "No se encontraron resultados"
                    : "No hay asignaturas disponibles"}
                </p>
                <p className={styles.emptyDescription}>
                  {searchTerm
                    ? "Intenta con otros términos de búsqueda"
                    : "Crea tu primera asignatura para comenzar"}
                </p>
              </div>
            );
          }

          // Si están agrupadas por curso
          if (groupByCourse) {
            return (
              <div className={styles.groupedList}>
                {(groupedSubjects as GroupedSubject[]).map((group) => (
                  <div key={group.courseTitle} className={styles.courseGroup}>
                    <h3 className={styles.courseGroupTitle}>
                      {group.courseTitle}
                    </h3>
                    <div className={styles.subjectsList}>
                      {group.subjects.map((subject) => (
                        <div key={subject.id} className={styles.subjectCard}>
                          <div className={styles.subjectInfo}>
                            <h3 className={styles.subjectName}>
                              {subject.name}
                            </h3>
                          </div>
                          <button
                            onClick={() => handleAccessSubject(subject.slug)}
                            className={styles.accessButton}
                          >
                            Ver detalles
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            );
          }

          // Si no están agrupadas
          return (
            <div className={styles.subjectsList}>
              {(groupedSubjects as Subject[]).map((subject) => (
                <div key={subject.id} className={styles.subjectCard}>
                  <div className={styles.subjectInfo}>
                    <h3 className={styles.subjectName}>{subject.name}</h3>
                    <p className={styles.courseName}>{subject.course.title}</p>
                  </div>
                  <button
                    onClick={() => handleAccessSubject(subject.slug)}
                    className={styles.accessButton}
                  >
                    Ver detalles
                  </button>
                </div>
              ))}
            </div>
          );
        })()}
      </div>
    </div>
  );
}
