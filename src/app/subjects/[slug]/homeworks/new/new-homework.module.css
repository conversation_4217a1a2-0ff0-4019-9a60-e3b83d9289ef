.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.backButton {
  background: none;
  border: none;
  color: var(--title-color);
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem 0;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.backButton:hover {
  color: var(--title-color);
}

.formCard {
  background-color: var(--bg-primary);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.header {
  margin-bottom: 2rem;
}

.courseBadge {
  display: inline-block;
  background: #f0f0f0;
  color: var(--title-color);
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--title-color);
}

.input {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  width: 100%;
  box-sizing: border-box;
}

.input:focus {
  outline: none;
  border-color: #0070f3;
  box-shadow: 0 0 0 2px rgba(0, 112, 243, 0.1);
}

.formActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.submitButton {
  background: #0070f3;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submitButton:hover {
  background: #0051b3;
}

.error {
  color: #dc2626;
  text-align: center;
  margin: 0;
}
