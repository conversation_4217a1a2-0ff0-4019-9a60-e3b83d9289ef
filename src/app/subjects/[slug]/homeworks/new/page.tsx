"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { useDomain } from "@/components/Context";
import styles from "./new-homework.module.css";

interface Subject {
  id: string;
  name: string;
  slug: string;
  description: string;
  course: {
    id: string;
    title: string;
  };
}

export default function NewHomeworkPage() {
  const router = useRouter();
  const params = useParams();
  const [subject, setSubject] = useState<Subject | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    due_date: "",
  });
  const { domain } = useDomain();

  useEffect(() => {
    const fetchSubject = async () => {
      try {
        const subjectData = await domain.getSubjectBySlugUseCase.execute(
          params.slug as string
        );
        setSubject(subjectData);
      } catch (err) {
        console.error("Error fetching subject:", err);
        setError("Error al cargar los detalles de la asignatura");
      } finally {
        setLoading(false);
      }
    };

    fetchSubject();
  }, [params.slug, domain.getSubjectBySlugUseCase]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!subject) return;

    try {
      await domain.createHomeworkUseCase.execute({
        ...formData,
        subject_id: subject.id,
      });
      router.push(`/subjects/${params.slug}`);
    } catch (err) {
      console.error("Error creating homework:", err);
      setError("Error al crear el trabajo");
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <p>Cargando...</p>
        </div>
      </div>
    );
  }

  if (error || !subject) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <p className={styles.error}>{error || "Asignatura no encontrada"}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <button onClick={() => router.back()} className={styles.backButton}>
        ← Volver
      </button>
      <div className={styles.formCard}>
        <div className={styles.header}>
          <div className={styles.courseBadge}>{subject.course.title}</div>
          <h1 className={styles.title}>Nuevo trabajo</h1>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="title" className={styles.label}>
              Título
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description" className={styles.label}>
              Descripción
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className={styles.input}
              rows={4}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="due_date" className={styles.label}>
              Fecha de entrega
            </label>
            <input
              type="date"
              id="due_date"
              name="due_date"
              value={formData.due_date}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.formActions}>
            <button type="submit" className={styles.submitButton}>
              Crear trabajo
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
