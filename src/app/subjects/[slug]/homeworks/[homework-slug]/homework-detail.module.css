.container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.titleContainer {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.title {
  font-size: 2rem;
  color: var(--title-color);
  margin: 0;
}

.subjectBadge {
  padding: 0.25rem 0.75rem;
  background-color: #e3f2fd;
  color: #1976d2;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  align-self: flex-start;
}

.actions {
  display: flex;
  gap: 1rem;
}

.editButton {
  padding: 0.5rem 1rem;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.editButton:hover {
  background-color: #1976d2;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section {
  background-color: #f9f9f9;
  padding: 1.5rem;
  border-radius: 4px;
}

.sectionTitle {
  font-size: 1.25rem;
  color: var(--title-color);
  margin: 0 0 1rem 0;
}

.description {
  color: var(--title-color);
  line-height: 1.6;
  margin: 0;
}

/* Estilos para la sección de criterios */
.criteriaSection {
  margin-top: 1rem;
}

.criteriasList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.assignedCriteria {
  background-color: #e8f5e9;
  border-left: 4px solid #4caf50;
  padding: 1rem;
  border-radius: 4px;
}

.criteriaName {
  font-size: 1.125rem;
  font-weight: 600;
  color: #2e7d32;
  margin: 0 0 0.5rem 0;
}

.criteriaDescription {
  color: var(--title-color);
  line-height: 1.6;
  margin: 0;
}

.noCriteria {
  color: #757575;
  font-style: italic;
}

/* Footer y botón de asignar */
.footer {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
}

.assignButton {
  padding: 0.75rem 1.5rem;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.assignButton:hover {
  background-color: #388e3c;
}

.loading,
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.125rem;
  color: #4a4a4a;
}

.error {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 1rem;
  border-radius: 4px;
  margin: 2rem auto;
  max-width: 600px;
}
