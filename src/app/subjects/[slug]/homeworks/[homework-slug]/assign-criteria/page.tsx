"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { useDomain } from "@/components/Context";
import styles from "./assign-criteria.module.css";
import { Breadcrumbs } from "@/components/Breadcrumbs/Breadcrumbs";
import { HomeworkWithSubject } from "@/domain/homeworks/useCases/GetHomeworksListUseCase";

interface Criteria {
  id: string;
  name: string;
  description: string;
  slug: string;
}

interface HomeworkDetail {
  id: string;
  title: string;
  description: string;
  slug: string;
  subject_id: string;
  criterias?: Array<{
    id: string;
    name: string;
    description: string;
    slug: string;
  }>;
  subject: {
    id: string;
    name: string;
    slug: string;
    course: {
      id: string;
      title: string;
      slug: string;
    };
  };
}

export default function AssignCriteriaPage() {
  const params = useParams();
  const router = useRouter();
  const [homework, setHomework] = useState<HomeworkDetail | null>(null);
  const [criterias, setCriterias] = useState<Criteria[]>([]);
  const [selectedCriteriaIds, setSelectedCriteriaIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { domain } = useDomain();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // Primero obtenemos la asignatura por su slug
        const subject = await domain.getSubjectBySlugUseCase.execute(
          params.slug as string
        );

        if (!subject) {
          setError("Asignatura no encontrada");
          return;
        }

        // Luego obtenemos el trabajo por su slug
        const homeworksList =
          await domain.getHomeworksBySubjectIdUseCase.execute(subject.id);

        const homeworkData = homeworksList.find(
          (hw: HomeworkWithSubject) => hw.slug === params["homework-slug"]
        );

        if (!homeworkData) {
          setError("Trabajo no encontrado");
          return;
        }

        setHomework({
          ...homeworkData,
          subject: {
            ...homeworkData.subject,
            course: subject.course,
          },
        });

        // Obtenemos los criterios de la asignatura
        const subjectWithCriterias =
          await domain.getSubjectWithCriteriasUseCase.execute(subject.id);

        if (subjectWithCriterias?.criterias) {
          setCriterias(subjectWithCriterias.criterias);

          // Si el trabajo ya tiene criterios asignados, los seleccionamos
          if (homeworkData.criterias && homeworkData.criterias.length > 0) {
            setSelectedCriteriaIds(
              homeworkData.criterias.map((c: Criteria) => c.id)
            );
          }
        }
      } catch (error) {
        console.error("Error al obtener datos:", error);
        setError(
          error instanceof Error ? error.message : "Error al obtener datos"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.slug, params["homework-slug"]]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!homework) return;

    try {
      setSubmitting(true);

      // Llamamos al caso de uso para asignar los criterios al trabajo
      await domain.assignCriteriasToHomeworkUseCase.execute({
        homework_id: homework.id,
        criteria_ids: selectedCriteriaIds, // Enviamos el array de IDs seleccionados
      });

      // Redirigimos a la página de detalle del trabajo
      router.push(
        `/subjects/${params.slug}/homeworks/${params["homework-slug"]}`
      );
    } catch (error) {
      console.error("Error al asignar criterios:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Error al asignar criterios al trabajo"
      );
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <div className={styles.loading}>Cargando...</div>;
  }

  if (error) {
    return <div className={styles.error}>{error}</div>;
  }

  if (!homework) {
    return <div className={styles.error}>Trabajo no encontrado</div>;
  }

  return (
    <div className={styles.container}>
      <Breadcrumbs
        items={[
          { label: "Cursos", href: "/courses" },
          {
            label: homework.subject.course.title,
            href: `/courses/${homework.subject.course.slug}`,
          },
          {
            label: homework.subject.name,
            href: `/subjects/${homework.subject.slug}`,
          },
          {
            label: homework.title,
            href: `/subjects/${homework.subject.slug}/homeworks/${homework.slug}`,
          },
          { label: "Gestionar Criterios" },
        ]}
      />
      <div className={styles.formCard}>
        <div className={styles.header}>
          <h1 className={styles.title}>Gestionar Criterios de Evaluación</h1>
          <span className={styles.homeworkTitle}>{homework.title}</span>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label className={styles.label}>
              Selecciona los criterios de evaluación
            </label>
            <div className={styles.criteriaCheckboxList}>
              {criterias.map((criteria) => (
                <div key={criteria.id} className={styles.criteriaCheckboxItem}>
                  <input
                    type="checkbox"
                    id={`criteria-${criteria.id}`}
                    checked={selectedCriteriaIds.includes(criteria.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedCriteriaIds([
                          ...selectedCriteriaIds,
                          criteria.id,
                        ]);
                      } else {
                        setSelectedCriteriaIds(
                          selectedCriteriaIds.filter((id) => id !== criteria.id)
                        );
                      }
                    }}
                    className={styles.checkbox}
                  />
                  <label
                    htmlFor={`criteria-${criteria.id}`}
                    className={styles.checkboxLabel}
                  >
                    <div className={styles.criteriaTitle}>{criteria.name}</div>
                    <div className={styles.criteriaDescription}>
                      {criteria.description}
                    </div>
                  </label>
                </div>
              ))}
            </div>
          </div>

          {selectedCriteriaIds.length > 0 && (
            <div className={styles.selectedCriteriaList}>
              <h3 className={styles.selectedCriteriaTitle}>
                Criterios seleccionados:
              </h3>
              {selectedCriteriaIds.map((id) => {
                const criteria = criterias.find((c) => c.id === id);
                return criteria ? (
                  <div key={criteria.id} className={styles.selectedCriteria}>
                    <h4 className={styles.criteriaTitle}>{criteria.name}</h4>
                    <p className={styles.criteriaDescription}>
                      {criteria.description}
                    </p>
                  </div>
                ) : null;
              })}
            </div>
          )}

          <div className={styles.formActions}>
            <button
              type="button"
              onClick={() =>
                router.push(
                  `/subjects/${homework.subject.slug}/homeworks/${homework.slug}`
                )
              }
              className={styles.cancelButton}
              disabled={submitting}
            >
              Cancelar
            </button>
            <button
              type="submit"
              className={styles.submitButton}
              disabled={submitting}
            >
              {submitting ? "Guardando..." : "Guardar"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
