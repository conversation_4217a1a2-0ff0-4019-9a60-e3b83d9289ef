"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { useDomain } from "@/components/Context";
import styles from "./edit-homework.module.css";
import { Breadcrumbs } from "@/components/Breadcrumbs/Breadcrumbs";

interface HomeworkDetail {
  id: string;
  title: string;
  description: string;
  slug: string;
  subject_id: string;
  criterias?: Array<{
    id: string;
    name: string;
    description: string;
    slug: string;
  }>;
  subject: {
    id: string;
    name: string;
    slug: string;
    course: {
      id: string;
      title: string;
      slug: string;
    };
  };
}

interface Criteria {
  id: string;
  name: string;
  description: string;
  slug: string;
}

export default function EditHomeworkPage() {
  const params = useParams();
  const router = useRouter();
  const [homework, setHomework] = useState<HomeworkDetail | null>(null);
  const [criterias, setCriterias] = useState<Criteria[]>([]);
  const [selectedCriteriaIds, setSelectedCriteriaIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
  });
  const { domain } = useDomain();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // Primero obtenemos la asignatura por su slug
        const subject = await domain.getSubjectBySlugUseCase.execute(
          params.slug as string
        );

        if (!subject) {
          setError("Asignatura no encontrada");
          return;
        }

        // Luego obtenemos el trabajo por su slug
        const homeworksList =
          await domain.getHomeworksBySubjectIdUseCase.execute(subject.id);

        const homeworkData = homeworksList.find(
          (hw: HomeworkDetail) => hw.slug === params["homework-slug"]
        );

        if (!homeworkData) {
          setError("Trabajo no encontrado");
          return;
        }

        setHomework({
          ...homeworkData,
          subject: {
            ...homeworkData.subject,
            course: subject.course,
          },
        });

        setFormData({
          title: homeworkData.title,
          description: homeworkData.description,
        });

        // Si el trabajo tiene criterios asignados, los seleccionamos
        if (homeworkData.criterias && homeworkData.criterias.length > 0) {
          setSelectedCriteriaIds(
            homeworkData.criterias.map((c: Criteria) => c.id)
          );
        }

        // Obtenemos todos los criterios disponibles para la asignatura
        const subjectWithCriterias =
          await domain.getSubjectWithCriteriasUseCase.execute(subject.id);

        if (subjectWithCriterias?.criterias) {
          setCriterias(subjectWithCriterias.criterias);
        }
      } catch (error) {
        console.error("Error al obtener el trabajo:", error);
        setError(
          error instanceof Error ? error.message : "Error al obtener el trabajo"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.slug, params["homework-slug"]]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!homework) return;

    try {
      // Primero actualizamos la información básica del trabajo
      const result = await domain.updateHomeworkUseCase.execute({
        id: homework.id,
        title: formData.title,
        description: formData.description,
        subject_id: homework.subject_id,
      });

      // Luego actualizamos los criterios asignados
      await domain.assignCriteriasToHomeworkUseCase.execute({
        homework_id: homework.id,
        criteria_ids: selectedCriteriaIds,
      });

      // Usamos el nuevo slug devuelto por el caso de uso para la redirección
      router.push(`/subjects/${params.slug}/homeworks/${result.slug}`);
    } catch (error) {
      console.error("Error al actualizar el trabajo:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Error al actualizar el trabajo"
      );
    }
  };

  const handleCriteriaChange = (criteriaId: string, checked: boolean) => {
    if (checked) {
      setSelectedCriteriaIds((prev) => [...prev, criteriaId]);
    } else {
      setSelectedCriteriaIds((prev) => prev.filter((id) => id !== criteriaId));
    }
  };

  if (loading) {
    return <div className={styles.loading}>Cargando...</div>;
  }

  if (error) {
    return <div className={styles.error}>{error}</div>;
  }

  if (!homework) {
    return <div className={styles.error}>Trabajo no encontrado</div>;
  }

  return (
    <div className={styles.container}>
      <Breadcrumbs
        items={[
          { label: "Cursos", href: "/courses" },
          {
            label: homework.subject.course.title,
            href: `/courses/${homework.subject.course.slug}`,
          },
          {
            label: homework.subject.name,
            href: `/subjects/${homework.subject.slug}`,
          },
          {
            label: homework.title,
            href: `/subjects/${homework.subject.slug}/homeworks/${homework.slug}`,
          },
          { label: "Editar" },
        ]}
      />
      <div className={styles.formCard}>
        <div className={styles.header}>
          <h1 className={styles.title}>Editar trabajo</h1>
          <span className={styles.subjectBadge}>{homework.subject.name}</span>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="title" className={styles.label}>
              Título
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description" className={styles.label}>
              Descripción
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className={styles.input}
              rows={4}
              required
            />
          </div>

          {/* Sección para gestionar criterios */}
          <div className={styles.formGroup}>
            <label className={styles.label}>Criterios de evaluación</label>
            <div className={styles.criteriaCheckboxList}>
              {criterias.length > 0 ? (
                criterias.map((criteria) => (
                  <div
                    key={criteria.id}
                    className={styles.criteriaCheckboxItem}
                  >
                    <input
                      type="checkbox"
                      id={`criteria-${criteria.id}`}
                      checked={selectedCriteriaIds.includes(criteria.id)}
                      onChange={(e) =>
                        handleCriteriaChange(criteria.id, e.target.checked)
                      }
                      className={styles.checkbox}
                    />
                    <label
                      htmlFor={`criteria-${criteria.id}`}
                      className={styles.checkboxLabel}
                    >
                      <div className={styles.criteriaTitle}>
                        {criteria.name}
                      </div>
                      <div className={styles.criteriaDescription}>
                        {criteria.description}
                      </div>
                    </label>
                  </div>
                ))
              ) : (
                <p className={styles.noCriterias}>
                  No hay criterios disponibles para esta asignatura
                </p>
              )}
            </div>
          </div>

          {/* Previsualización de criterios seleccionados */}
          {selectedCriteriaIds.length > 0 && (
            <div className={styles.selectedCriteriaList}>
              <h3 className={styles.selectedCriteriaTitle}>
                Criterios seleccionados:
              </h3>
              {selectedCriteriaIds.map((id) => {
                const criteria = criterias.find((c) => c.id === id);
                return criteria ? (
                  <div key={criteria.id} className={styles.selectedCriteria}>
                    <h4 className={styles.criteriaTitle}>{criteria.name}</h4>
                    <p className={styles.criteriaDescription}>
                      {criteria.description}
                    </p>
                  </div>
                ) : null;
              })}
            </div>
          )}

          <div className={styles.formActions}>
            <button
              type="button"
              onClick={() =>
                router.push(
                  `/subjects/${homework.subject.slug}/homeworks/${homework.slug}`
                )
              }
              className={styles.cancelButton}
            >
              Cancelar
            </button>
            <button type="submit" className={styles.submitButton}>
              Guardar cambios
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
