.container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.formCard {
  background-color: var(--bg-primary);
  border-radius: 8px;
  padding: 2rem;
}

.header {
  margin-bottom: 2rem;
}

.title {
  font-size: 1.75rem;
  color: var(--title-color);
  margin: 0 0 0.5rem 0;
}

.subjectBadge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: #e3f2fd;
  color: #1976d2;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a4a4a;
}

.input {
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 1rem;
  color: var(--title-color);
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: #2196f3;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.submitButton {
  padding: 0.75rem 1.5rem;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submitButton:hover {
  background-color: #1976d2;
}

.cancelButton {
  padding: 0.75rem 1.5rem;
  background-color: #f5f5f5;
  color: #4a4a4a;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.cancelButton:hover {
  background-color: #e0e0e0;
}

.loading,
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.125rem;
  color: #4a4a4a;
}

.error {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 1rem;
  border-radius: 4px;
  margin: 2rem auto;
  max-width: 600px;
}

/* Estilos para la sección de criterios */
.criteriaCheckboxList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
  padding: 0.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.criteriaCheckboxItem {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 4px;
  background-color: var(--bg-primary);
  transition: background-color 0.2s ease;
  margin-bottom: 0.75rem;
  border: 1px solid #e0e0e0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.criteriaCheckboxItem:hover {
  background-color: #f5f5f5;
}

.checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  margin-top: 0.25rem;
}

.checkboxLabel {
  font-size: 1rem;
  color: var(--title-color);
  cursor: pointer;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.noCriterias {
  color: #757575;
  font-style: italic;
  padding: 0.5rem;
}

.selectedCriteriaList {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.selectedCriteriaTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
}

.selectedCriteria {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  border-left: 4px solid #2196f3;
}

.criteriaTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0 0 0.5rem 0;
}

.criteriaDescription {
  color: #4a4a4a;
  line-height: 1.6;
  margin: 0;
  font-size: 0.875rem;
}

/* Estilos específicos para los checkboxes */
.checkboxLabel .criteriaTitle {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #2196f3;
}

.checkboxLabel .criteriaDescription {
  font-size: 0.875rem;
  color: var(--title-color);
  line-height: 1.5;
}
