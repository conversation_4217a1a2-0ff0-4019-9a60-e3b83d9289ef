"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { useDomain } from "@/components/Context";
import styles from "./homework-detail.module.css";
import { Breadcrumbs } from "@/components/Breadcrumbs/Breadcrumbs";

interface HomeworkDetail {
  id: string;
  title: string;
  description: string;
  slug: string;
  criterias?: Array<{
    id: string;
    name: string;
    description: string;
    slug: string;
  }>;
  subject: {
    id: string;
    name: string;
    slug: string;
    course: {
      id: string;
      title: string;
      slug: string;
    };
  };
}

export default function HomeworkDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [homework, setHomework] = useState<HomeworkDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { domain } = useDomain();

  useEffect(() => {
    const fetchHomework = async () => {
      try {
        setLoading(true);
        // Primero obtenemos la asignatura por su slug
        const subject = await domain.getSubjectBySlugUseCase.execute(
          params.slug as string
        );

        if (!subject) {
          setError("Asignatura no encontrada");
          return;
        }

        // Luego obtenemos el trabajo por su slug
        const homeworksList =
          await domain.getHomeworksBySubjectIdUseCase.execute(subject.id);

        const homeworkData = homeworksList.find(
          (hw: any) => hw.slug === params["homework-slug"]
        );

        if (!homeworkData) {
          setError("Trabajo no encontrado");
          return;
        }

        setHomework({
          ...homeworkData,
          subject: {
            ...homeworkData.subject,
            course: subject.course,
          },
        });
      } catch (error) {
        console.error("Error al obtener el trabajo:", error);
        setError(
          error instanceof Error ? error.message : "Error al obtener el trabajo"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchHomework();
  }, [params.slug, params["homework-slug"]]);

  if (loading) {
    return <div className={styles.loading}>Cargando...</div>;
  }

  if (error) {
    return <div className={styles.error}>{error}</div>;
  }

  if (!homework) {
    return <div className={styles.error}>Trabajo no encontrado</div>;
  }

  return (
    <div className={styles.container}>
      <Breadcrumbs
        items={[
          { label: "Cursos", href: "/courses" },
          {
            label: homework.subject.course.title,
            href: `/courses/${homework.subject.course.slug}`,
          },
          {
            label: homework.subject.name,
            href: `/subjects/${homework.subject.slug}`,
          },
          { label: homework.title },
        ]}
      />
      <div className={styles.homeworkDetail}>
        <div className={styles.header}>
          <div className={styles.titleContainer}>
            <h1 className={styles.title}>{homework.title}</h1>
            <span className={styles.subjectBadge}>{homework.subject.name}</span>
          </div>
          <div className={styles.actions}>
            <button
              onClick={() =>
                router.push(
                  `/subjects/${homework.subject.slug}/homeworks/${homework.slug}/edit`
                )
              }
              className={styles.editButton}
            >
              Editar
            </button>
          </div>
        </div>

        <div className={styles.content}>
          <div className={styles.section}>
            <h2 className={styles.sectionTitle}>Descripción</h2>
            <p className={styles.description}>{homework.description}</p>
          </div>

          {/* Sección para mostrar los criterios asignados (si existen) */}
          <div className={styles.section}>
            <h2 className={styles.sectionTitle}>
              Criterios de evaluación asignados
            </h2>
            <div className={styles.criteriaSection}>
              {homework.criterias && homework.criterias.length > 0 ? (
                <div className={styles.criteriasList}>
                  {homework.criterias.map((criteria) => (
                    <div key={criteria.id} className={styles.assignedCriteria}>
                      <h3 className={styles.criteriaName}>{criteria.name}</h3>
                      <p className={styles.criteriaDescription}>
                        {criteria.description}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className={styles.noCriteria}>
                  No hay criterios asignados a este trabajo
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Footer con botón para gestionar criterios */}
        <div className={styles.footer}>
          <button
            onClick={() =>
              router.push(
                `/subjects/${homework.subject.slug}/homeworks/${homework.slug}/assign-criteria`
              )
            }
            className={styles.assignButton}
          >
            Gestionar Criterios
          </button>
        </div>
      </div>
    </div>
  );
}
