.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
  padding: 2rem;
}

.subjectDetail {
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: linear-gradient(
    135deg,
    rgba(29, 136, 175, 0.1) 0%,
    rgba(56, 189, 248, 0.1) 100%
  );
  border-bottom: 1px solid var(--border-color);
}

.titleContainer {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--title-color);
  margin: 0;
  line-height: 1.2;
}

.courseBadge {
  padding: 0.25rem 0.75rem;
  background-color: rgba(29, 136, 175, 0.1);
  color: var(--primary);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  max-width: fit-content;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.actions {
  display: flex;
  gap: 1rem;
}

.editButton {
  padding: 0.75rem 1.25rem;
  background: #0284c7;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(29, 136, 175, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

.editButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(29, 136, 175, 0.3);
  background: #0369a1;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
}

.section {
  background-color: var(--bg-primary);
  padding: 1.5rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
}

.addButton {
  padding: 0.75rem 1.25rem;
  background: #0284c7;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(29, 136, 175, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.addButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(29, 136, 175, 0.3);
  background: #0369a1;
}

.criteriasList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.criteriaItem {
  background-color: var(--bg-primary);
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.criteriaItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.criteriaHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.criteriaTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
}

.indicatorsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.indicatorItem {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.25rem;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.indicatorItem:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.indicatorHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.indicatorTitle {
  color: var(--title-color);
  font-size: 1.1rem;
  font-weight: 600;
}

.levelsList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
}

.levelBadge {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: rgba(29, 136, 175, 0.1);
  color: var(--primary);
  border-radius: 6px;
  font-size: 0.9rem;
  text-align: left;
  transition: background-color 0.2s ease;
}

.levelBadge:hover {
  background-color: rgba(29, 136, 175, 0.2);
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.active {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.inactive {
  background-color: #ffebee;
  color: #c62828;
}

.detailButton {
  padding: 0.75rem 1.25rem;
  background: #0284c7;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(29, 136, 175, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  align-self: flex-end;
  margin-top: auto;
}

.detailButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(29, 136, 175, 0.3);
  background: #0369a1;
}

.emptyMessage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px dashed var(--border-color);
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.homeworksList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.homeworkItem {
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  background-color: var(--bg-primary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.homeworkItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.homeworkInfo {
  flex: 1;
  margin-bottom: 1.5rem;
}

.homeworkTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0 0 0.75rem 0;
}

.homeworkDescription {
  color: var(--text-secondary);
  font-size: 0.95rem;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.criteriaBadgesContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.criteriaBadge {
  padding: 0.25rem 0.75rem;
  background-color: rgba(29, 136, 175, 0.1);
  color: var(--primary);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
}

.loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: 1.125rem;
  color: var(--text-secondary);
  gap: 1rem;
}

.loading::before {
  content: "";
  width: 40px;
  height: 40px;
  border: 3px solid rgba(29, 136, 175, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #d32f2f;
  background-color: rgba(211, 47, 47, 0.1);
  padding: 2rem;
  border-radius: 12px;
  margin: 2rem auto;
  max-width: 800px;
  text-align: center;
  gap: 1rem;
}
