.container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.subjectDetail {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.backButton {
  background: none;
  border: none;
  color: #64748b;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: color 0.2s ease;
  margin-bottom: 1rem;
}

.backButton:hover {
  color: var(--title-color);
}

.title {
  font-size: 2rem;
  color: var(--title-color);
  margin: 0;
}

.courseBadge {
  display: inline-block;
  background-color: #f1f5f9;
  color: #64748b;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  white-space: nowrap;
  align-self: flex-start;
}

.info {
  margin-bottom: 2rem;
}

.description {
  color: var(--title-color);
  font-size: 1.125rem;
  line-height: 1.6;
  margin: 0;
}

.homeworksSection {
  margin-top: 2rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.sectionHeader h2 {
  font-size: 1.5rem;
  color: var(--title-color);
  margin: 0;
}

.addButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: #f1f5f9;
  color: #64748b;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: background-color 0.2s ease;
  width: 100%;
  margin-top: 1rem;
}

.addButton:hover {
  background: #e2e8f0;
}

.homeworksList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.homeworkItem {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  background-color: var(--bg-primary);
  padding: 1.5rem;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.homeworkItem:hover {
  background-color: #f1f5f9;
}

.homeworkInfo {
  flex: 1;
}

.homeworkTitle {
  font-size: 1.25rem;
  color: var(--title-color);
  margin: 0 0 0.5rem 0;
}

.homeworkDescription {
  color: #64748b;
  margin: 0 0 0.5rem 0;
  line-height: 1.5;
}

.dueDate {
  color: #94a3b8;
  font-size: 0.875rem;
}

.accessButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.accessButton:hover {
  background-color: #2563eb;
}

.emptyMessage {
  color: #64748b;
  text-align: center;
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 8px;
}

.error {
  color: #ef4444;
  text-align: center;
  padding: 2rem;
  background-color: #fef2f2;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .subjectDetail {
    padding: 1.5rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .homeworkItem {
    flex-direction: column;
    gap: 1rem;
  }

  .accessButton {
    width: 100%;
  }
}
