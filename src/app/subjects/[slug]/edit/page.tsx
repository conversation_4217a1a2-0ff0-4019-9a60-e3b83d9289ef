"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import styles from "./edit-subject.module.css";
import { useDomain } from "@/components/Context";
import { supabase } from "@/utils/supabase/client";
import { Breadcrumbs } from "@/components/Breadcrumbs/Breadcrumbs";

interface Course {
  id: string;
  title: string;
  description: string;
  slug: string;
}

interface Subject {
  id: string;
  name: string;
  slug: string;
  course: {
    id: string;
    title: string;
    slug: string;
  };
}

export default function EditSubjectPage() {
  const router = useRouter();
  const params = useParams();
  const [courses, setCourses] = useState<Course[]>([]);
  const [subject, setSubject] = useState<Subject | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    courseId: "",
  });
  const { domain } = useDomain();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // Get current user session
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!session?.user) {
          throw new Error("No hay usuario autenticado");
        }

        // Get the subject by slug
        const slug = params.slug as string;
        const subjectData = await domain.getSubjectBySlugUseCase.execute(slug);

        if (!subjectData) {
          setError("Asignatura no encontrada");
          return;
        }

        setSubject(subjectData);

        // Set initial form data
        setFormData({
          name: subjectData.name,
          courseId: subjectData.course.id,
        });

        // Get courses list for the dropdown
        const coursesList = await domain.getCoursesListUseCase.execute(
          session.user.id
        );
        setCourses(coursesList);
      } catch (err) {
        console.error("Error fetching data:", err);
        setError("Error al cargar los datos");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.slug]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (!subject) return;

      // Check if course has changed
      const courseChanged = formData.courseId !== subject.course.id;

      // Check if name has changed
      const nameChanged = formData.name !== subject.name;

      // Only update if something has changed
      if (nameChanged || courseChanged) {
        // Update the subject with all changes
        const updatedSubject = await domain.updateSubjectUseCase.execute({
          id: subject.id,
          name: formData.name,
          courseId: courseChanged ? formData.courseId : undefined,
          // If the name has changed, the slug will be automatically updated in the repository
        });

        // Redirect to the updated subject page using the new slug
        // The slug is automatically updated in the repository if the name changes
        if (nameChanged) {
          router.push(`/subjects/${updatedSubject.slug}`);
        } else {
          router.push(`/subjects/${subject.slug}`);
        }
      } else {
        // No changes, just go back
        router.push(`/subjects/${subject.slug}`);
      }
    } catch (err) {
      console.error("Error updating subject:", err);
      setError("Error al actualizar la asignatura");
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <p>Cargando...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <p className={styles.error}>{error}</p>
          <button onClick={() => router.back()} className={styles.backButton}>
            ← Volver
          </button>
        </div>
      </div>
    );
  }

  if (!subject) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <p className={styles.error}>Asignatura no encontrada</p>
          <button onClick={() => router.back()} className={styles.backButton}>
            ← Volver
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Breadcrumbs
        items={[
          { label: "Cursos", href: "/courses" },
          {
            label: subject.course.title,
            href: `/courses/${subject.course.slug}`,
          },
          {
            label: subject.name,
            href: `/subjects/${subject.slug}`,
          },
          { label: "Editar" },
        ]}
      />

      <div className={styles.formCard}>
        <div className={styles.header}>
          <h1 className={styles.title}>Editar Asignatura</h1>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="name" className={styles.label}>
              Título
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="courseId" className={styles.label}>
              Curso
            </label>
            <select
              id="courseId"
              name="courseId"
              value={formData.courseId}
              onChange={handleChange}
              className={styles.select}
              required
            >
              <option value="">Selecciona un curso</option>
              {courses.map((course) => (
                <option key={course.id} value={course.id}>
                  {course.title}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.formActions}>
            <button
              type="button"
              onClick={() => router.back()}
              className={styles.cancelButton}
            >
              Cancelar
            </button>
            <button type="submit" className={styles.submitButton}>
              Guardar cambios
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
