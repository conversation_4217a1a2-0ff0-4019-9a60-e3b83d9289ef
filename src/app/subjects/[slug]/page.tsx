"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { useDomain } from "@/components/Context";
import { Course } from "@/domain/courses/types";
import { HomeworkWithSubject } from "@/domain/homeworks/useCases/GetHomeworksListUseCase";
import styles from "./subject.module.css";
import { Breadcrumbs } from "@/components/Breadcrumbs/Breadcrumbs";

interface SubjectWithCourse {
  subject: {
    id: string;
    name: string;
    slug: string;
    course: Course;
  };
  criterias: Array<{
    id: string;
    name: string;
    slug: string;
    description: string;
    indicators: Array<{
      id: string;
      title: string;
      slug: string;
      levels: Array<{
        id: string;
        level_number: number;
        title: string;
      }>;
    }>;
  }>;
}

// Extendemos la interfaz HomeworkWithSubject para incluir los criterios
interface HomeworkWithCriterias extends HomeworkWithSubject {
  criterias?: Array<{
    id: string;
    name: string;
    description: string;
    slug: string;
  }>;
}

export default function SubjectDetailPage() {
  const { domain } = useDomain();
  const params = useParams();
  const router = useRouter();
  const [subjectData, setSubjectData] = useState<SubjectWithCourse | null>(
    null
  );
  const [homeworks, setHomeworks] = useState<HomeworkWithCriterias[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const slug = params.slug as string;
        const subject = await domain.getSubjectBySlugUseCase.execute(slug);
        console.log("Subject data:", subject);
        console.log("Course data:", subject?.course);

        if (!subject) {
          setError("Asignatura no encontrada");
          return;
        }

        // Obtener criterios de la asignatura
        const subjectWithCriterias =
          await domain.getSubjectWithCriteriasUseCase.execute(subject.id);
        if (!subjectWithCriterias) {
          setError("Error al obtener los criterios de la asignatura");
          return;
        }

        setSubjectData(subjectWithCriterias);

        // Obtener trabajos de la asignatura
        const homeworksList =
          await domain.getHomeworksBySubjectIdUseCase.execute(subject.id);

        // Los trabajos ya deberían incluir los criterios desde el repositorio
        // según vimos en el código de HttpRepository.getHomeworksBySubjectId
        setHomeworks(homeworksList as HomeworkWithCriterias[]);
      } catch (error) {
        console.error("Error al obtener la asignatura:", error);
        setError(
          error instanceof Error
            ? error.message
            : "Error al obtener la asignatura"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.slug]);

  if (loading) {
    return <div className={styles.loading}>Cargando...</div>;
  }

  if (error) {
    return <div className={styles.error}>{error}</div>;
  }

  if (!subjectData) {
    return <div className={styles.error}>Asignatura no encontrada</div>;
  }

  const { subject, criterias } = subjectData;

  return (
    <div className={styles.container}>
      <Breadcrumbs
        items={[
          { label: "Cursos", href: "/courses" },
          {
            label: subject.course.title,
            href: `/courses/${subject.course.slug}`,
          },
          { label: subject.name },
        ]}
      />
      <div className={styles.subjectDetail}>
        <div className={styles.header}>
          <div className={styles.titleContainer}>
            <h1 className={styles.title}>{subject.name}</h1>
            <span className={styles.courseBadge}>{subject.course.title}</span>
          </div>
          <div className={styles.actions}>
            <button
              onClick={() => router.push(`/subjects/${subject.slug}/edit`)}
              className="btn-secondary"
            >
              Editar
            </button>
          </div>
        </div>

        <div className={styles.content}>
          <div className={styles.section}>
            <div className={styles.sectionHeader}>
              <h2 className={styles.sectionTitle}>Criterios de evaluación</h2>
              <button
                onClick={() =>
                  router.push(`/criterias/new?subject_id=${subject.id}`)
                }
                className="btn-add btn-icon"
              >
                <span>+</span> Nuevo criterio
              </button>
            </div>

            <div className={styles.criteriasList}>
              {criterias.map((criteria) => (
                <div key={criteria.id} className={styles.criteriaItem}>
                  <div className={styles.criteriaHeader}>
                    <h3 className={styles.criteriaTitle}>{criteria.name}</h3>
                    <button
                      onClick={() =>
                        router.push(`/criterias/${criteria.slug}/edit`)
                      }
                      className="btn-secondary btn-sm"
                    >
                      Editar
                    </button>
                  </div>

                  <div className={styles.indicatorsList}>
                    {criteria.indicators.map((indicator) => (
                      <div key={indicator.id} className={styles.indicatorItem}>
                        <div className={styles.indicatorHeader}>
                          <span className={styles.indicatorTitle}>
                            {indicator.title}
                          </span>
                          <button
                            onClick={() =>
                              router.push(`/indicators/${indicator.slug}`)
                            }
                            className="btn-primary btn-sm"
                          >
                            Ver detalle
                          </button>
                        </div>
                        <div className={styles.indicatorInfo}>
                          <div className={styles.levelsList}>
                            {indicator.levels.map((level) => (
                              <span
                                key={level.id}
                                className={styles.levelBadge}
                              >
                                {level.title}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                    <button
                      onClick={() => router.push(`/indicators/new`)}
                      className="btn-add btn-icon btn-sm"
                    >
                      <span>+</span> Nuevo indicador
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className={styles.section}>
            <div className={styles.sectionHeader}>
              <h2 className={styles.sectionTitle}>Trabajos</h2>
              <button
                onClick={() =>
                  router.push(`/homeworks/new?subject_id=${subject.id}`)
                }
                className="btn-add btn-icon"
              >
                <span>+</span> Nuevo trabajo
              </button>
            </div>
            {homeworks.length === 0 ? (
              <div className={styles.emptyMessage}>
                No hay trabajos registrados para esta asignatura
              </div>
            ) : (
              <div className={styles.homeworksList}>
                {homeworks.map((homework) => (
                  <div key={homework.id} className={styles.homeworkItem}>
                    <div className={styles.homeworkInfo}>
                      <h3 className={styles.homeworkTitle}>{homework.title}</h3>
                      <p className={styles.homeworkDescription}>
                        {homework.description}
                      </p>
                      {homework.criterias && homework.criterias.length > 0 ? (
                        <div className={styles.criteriaBadgesContainer}>
                          {homework.criterias.map((criteria) => (
                            <span
                              key={criteria.id}
                              className={styles.criteriaBadge}
                            >
                              {criteria.name}
                            </span>
                          ))}
                        </div>
                      ) : (
                        <div className={styles.criteriaBadgesContainer}>
                          <span
                            className={styles.criteriaBadge}
                            style={{
                              backgroundColor: "#ffebee",
                              color: "#c62828",
                            }}
                          >
                            Sin criterios asignados
                          </span>
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() =>
                        router.push(
                          `/subjects/${subject.slug}/homeworks/${homework.slug}`
                        )
                      }
                      className="btn-primary"
                    >
                      Ver detalle
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
