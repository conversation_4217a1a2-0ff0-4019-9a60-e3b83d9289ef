"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import styles from "./new-subject.module.css";
import { useDomain } from "@/components/Context";
import { supabase } from "@/utils/supabase/client";

interface Course {
  id: string;
  title: string;
  description: string;
  slug: string;
}

export default function NewSubjectPage() {
  const router = useRouter();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    courseId: "",
  });
  const { domain } = useDomain();

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        // Obtener la sesión del usuario actual
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!session?.user) {
          throw new Error("No hay usuario autenticado");
        }

        // Pasar el ID del usuario al caso de uso
        const coursesList = await domain.getCoursesListUseCase.execute(
          session.user.id
        );
        setCourses(coursesList);
      } catch (err) {
        console.error("Error fetching courses:", err);
        setError("Error al cargar la lista de cursos");
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await domain.addSubjectUseCase.execute({
        name: formData.name,
        courseId: formData.courseId,
      });
      router.push("/subjects");
    } catch (err) {
      console.error("Error adding subject:", err);
      setError("Error al crear la asignatura");
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <p>Cargando...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <p className={styles.error}>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <button onClick={() => router.back()} className={styles.backButton}>
        ← Volver
      </button>

      <div className={styles.formCard}>
        <div className={styles.header}>
          <h1 className={styles.title}>Nueva Asignatura</h1>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="name" className={styles.label}>
              Título
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="courseId" className={styles.label}>
              Curso
            </label>
            <select
              id="courseId"
              name="courseId"
              value={formData.courseId}
              onChange={handleChange}
              className={styles.select}
              required
            >
              <option value="">Selecciona un curso</option>
              {courses.map((course) => (
                <option key={course.id} value={course.id}>
                  {course.title}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.formActions}>
            <button type="submit" className={styles.submitButton}>
              Crear asignatura
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
