/* Container principal */
.container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 92px);
  padding: 0;
  width: 100%;
  overflow-x: hidden;
}

/* Hero Section */
.hero {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 4rem 2rem;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
  min-height: 80vh;
  position: relative;
}

.heroContent {
  flex: 1;
  max-width: 600px;
  text-align: left;
  z-index: 2;
}

.heroImageContainer {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.heroImage {
  position: relative;
  width: 100%;
  max-width: 500px;
  height: auto;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.title {
  font-size: 4.5rem;
  font-weight: 800;
  color: var(--title-color);
  margin-bottom: 0.5rem;
  line-height: 1.1;
  letter-spacing: -1px;
}

.titleHighlight {
  background: linear-gradient(135deg, var(--primary) 0%, #38bdf8 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.titleSecondary {
  font-weight: 700;
  color: var(--text-secondary);
  font-size: 3.5rem;
  margin-left: 0.5rem;
}

.subtitle {
  font-size: 1.5rem;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.description {
  font-size: 1.25rem;
  line-height: 1.6;
  color: var(--text-primary);
  margin-bottom: 2.5rem;
  max-width: 800px;
  font-weight: 400;
}

.ctaContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
}

.ctaButton {
  background: linear-gradient(135deg, var(--primary) 0%, #38bdf8 100%);
  color: white;
  border: none;
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(29, 136, 175, 0.3);
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(29, 136, 175, 0.4);
}

.ctaButtonLarge {
  background: linear-gradient(135deg, var(--primary) 0%, #38bdf8 100%);
  color: white;
  border: none;
  padding: 1.2rem 3rem;
  font-size: 1.2rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(29, 136, 175, 0.3);
}

.ctaButtonLarge:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(29, 136, 175, 0.4);
}

.loginMessage {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 400;
}

/* Features Section */
.featuresSection {
  padding: 5rem 2rem;
  background-color: var(--bg-secondary);
  width: 100%;
}

.sectionTitle {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--title-color);
  margin-bottom: 3rem;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1280px;
  margin: 0 auto;
}

.featureCard {
  background-color: var(--bg-primary);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.featureCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.featureIconContainer {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(29, 136, 175, 0.1) 0%,
    rgba(56, 189, 248, 0.1) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.featureIcon {
  font-size: 2rem !important;
  color: var(--primary);
}

.featureTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 1rem;
}

.featureDescription {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* How It Works Section */
.howItWorksSection {
  padding: 5rem 2rem;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
}

.stepsContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 2rem;
  margin-top: 3rem;
}

.step {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
}

.step:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 2.5rem;
  right: -1rem;
  width: 2rem;
  height: 2px;
  background-color: var(--border-color);
}

.stepNumber {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary) 0%, #38bdf8 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 15px rgba(29, 136, 175, 0.3);
}

.stepTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 1rem;
}

.stepDescription {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* CTA Section */
.ctaSection {
  padding: 5rem 2rem;
  background: linear-gradient(
    135deg,
    rgba(29, 136, 175, 0.1) 0%,
    rgba(56, 189, 248, 0.1) 100%
  );
  width: 100%;
}

.ctaContent {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--title-color);
  margin-bottom: 1.5rem;
}

.ctaDescription {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .hero {
    flex-direction: column;
    text-align: center;
    padding: 3rem 1.5rem;
  }

  .heroContent {
    max-width: 100%;
    margin-bottom: 3rem;
    text-align: center;
  }

  .ctaContainer {
    align-items: center;
  }

  .stepsContainer {
    flex-direction: column;
  }

  .step:not(:last-child)::after {
    display: none;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 3rem;
  }

  .titleSecondary {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.25rem;
  }

  .description {
    font-size: 1rem;
  }

  .featuresGrid {
    grid-template-columns: 1fr;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .ctaTitle {
    font-size: 2rem;
  }

  .ctaDescription {
    font-size: 1rem;
  }
}
