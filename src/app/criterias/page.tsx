"use client";

import { useEffect, useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useDomain } from "@/components/Context";
import styles from "./criterias.module.css";
import { ConfirmModal } from "@/components/ConfirmModal/ConfirmModal";
import { Criteria } from "@/domain/criterias/types";
import { SearchIcon, EmptyImageIcon, CloseIcon } from "@/components/icons";
import { HeroSection, HeroVariant } from "@/components/HeroSection";

interface Subject {
  id: string;
  name: string;
  slug: string;
}

interface CriteriaWithSubject extends Omit<Criteria, "subject_id"> {
  subject: Subject;
}

type GroupedCriterias = [string, CriteriaWithSubject[]][];

export default function CriteriasPage() {
  const router = useRouter();
  const [criterias, setCriterias] = useState<CriteriaWithSubject[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState("");
  const [groupBySubject, setGroupBySubject] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [criteriaToDelete, setCriteriaToDelete] =
    useState<CriteriaWithSubject | null>(null);
  const { domain } = useDomain();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const criteriasList = await domain.getCriteriasUseCase.execute();
        setCriterias(criteriasList);
      } catch (err) {
        console.error("Error al cargar los criterios:", err);
        setError("Error al cargar los criterios");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [domain]);

  const filteredAndGroupedCriterias = useMemo(() => {
    let filtered = criterias;

    if (search) {
      const searchLower = search.toLowerCase();
      filtered = criterias.filter(
        (criteria) =>
          criteria.name.toLowerCase().includes(searchLower) ||
          criteria.subject.name.toLowerCase().includes(searchLower)
      );
    }

    if (!groupBySubject) {
      return filtered;
    }

    const grouped = filtered.reduce<Record<string, CriteriaWithSubject[]>>(
      (acc, criteria) => {
        const subjectName = criteria.subject.name;
        if (!acc[subjectName]) {
          acc[subjectName] = [];
        }
        acc[subjectName].push(criteria);
        return acc;
      },
      {}
    );

    return Object.entries(grouped).sort(([a], [b]) => a.localeCompare(b));
  }, [criterias, search, groupBySubject]) as
    | CriteriaWithSubject[]
    | GroupedCriterias;

  const handleDeleteClick = (criteria: CriteriaWithSubject) => {
    setCriteriaToDelete(criteria);
    setDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!criteriaToDelete) return;

    try {
      await domain.deleteCriteriaUseCase.execute(criteriaToDelete.id);

      setCriterias((prev) =>
        prev.filter((crit) => crit.id !== criteriaToDelete.id)
      );

      setDeleteModalOpen(false);
      setCriteriaToDelete(null);
    } catch (error) {
      console.error("Error al eliminar el criterio:", error);
      setError(
        error instanceof Error ? error.message : "Error al eliminar el criterio"
      );
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div className={styles.loadingState}>
          <div className={styles.loadingSpinner}></div>
          <p>Cargando criterios...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className={styles.errorState}>
          <p className={styles.errorMessage}>{error}</p>
          <button
            onClick={() => window.location.reload()}
            className={styles.retryButton}
          >
            Reintentar
          </button>
        </div>
      );
    }

    if (
      (!groupBySubject &&
        (filteredAndGroupedCriterias as CriteriaWithSubject[]).length === 0) ||
      (groupBySubject &&
        (filteredAndGroupedCriterias as GroupedCriterias).length === 0)
    ) {
      return (
        <div className={styles.emptyState}>
          <div className={styles.emptyStateIcon}>
            <EmptyImageIcon style={{ color: "var(--text-secondary)" }} />
          </div>
          <p className={styles.emptyMessage}>
            {search
              ? "No se encontraron resultados"
              : "No hay criterios disponibles"}
          </p>
          <p className={styles.emptyDescription}>
            {search
              ? "Intenta con otros términos de búsqueda"
              : "Crea tu primer criterio para comenzar a evaluar"}
          </p>
        </div>
      );
    }

    if (!groupBySubject) {
      return (
        <div className={styles.criteriaGrid}>
          {(filteredAndGroupedCriterias as CriteriaWithSubject[]).map(
            (criteria) => (
              <div key={criteria.id} className={styles.criteriaCard}>
                <div className={styles.criteriaHeader}>
                  <div className={styles.subjectTag}>
                    {criteria.subject.name}
                  </div>
                  <button
                    onClick={() => handleDeleteClick(criteria)}
                    className="btn-close"
                    aria-label={`Eliminar criterio ${criteria.name}`}
                  >
                    <CloseIcon />
                  </button>
                </div>
                <div className={styles.criteriaContent}>
                  <h3 className={styles.criteriaTitle}>{criteria.name}</h3>
                  <p className={styles.criteriaDescription}>
                    {criteria.description}
                  </p>
                </div>
                <div className={styles.criteriaFooter}>
                  <button
                    onClick={() => router.push(`/criterias/${criteria.slug}`)}
                    className="btn-primary"
                  >
                    Ver detalles
                  </button>
                </div>
              </div>
            )
          )}
        </div>
      );
    }

    return (
      <div className={styles.groupedList}>
        {(filteredAndGroupedCriterias as GroupedCriterias).map(
          ([subjectName, subjectCriterias]) => (
            <div key={subjectName} className={styles.subjectGroup}>
              <h3 className={styles.subjectTitle}>{subjectName}</h3>
              <div className={styles.criteriaGrid}>
                {subjectCriterias.map((criteria) => (
                  <div key={criteria.id} className={styles.criteriaCard}>
                    <div className={styles.criteriaHeader}>
                      <button
                        onClick={() => handleDeleteClick(criteria)}
                        className="btn-close"
                        aria-label={`Eliminar criterio ${criteria.name}`}
                      >
                        <CloseIcon />
                      </button>
                    </div>
                    <div className={styles.criteriaContent}>
                      <h3 className={styles.criteriaTitle}>{criteria.name}</h3>
                      <p className={styles.criteriaDescription}>
                        {criteria.description}
                      </p>
                    </div>
                    <div className={styles.criteriaFooter}>
                      <button
                        onClick={() =>
                          router.push(`/criterias/${criteria.slug}`)
                        }
                        className="btn-primary"
                      >
                        Ver detalles
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        )}
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <HeroSection
        title="Criterios"
        description="Los criterios de evaluación definen las áreas específicas que se evaluarán en cada asignatura. Cada criterio puede contener múltiples indicadores que permiten medir el desempeño de los estudiantes de manera objetiva y estructurada."
        ctaText="Añadir criterio"
        ctaUrl="/criterias/new"
        variant={HeroVariant.CRITERIAS}
      />

      <div className={styles.filtersSection}>
        <div className={styles.searchContainer}>
          <input
            type="text"
            placeholder="Buscar criterios..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className={styles.searchInput}
          />
          <SearchIcon
            className={styles.searchIcon}
            style={{ color: "var(--text-secondary)" }}
          />
        </div>
        <div className={styles.groupToggle}>
          <label htmlFor="groupToggle">Agrupar por asignatura</label>
          <input
            id="groupToggle"
            type="checkbox"
            checked={groupBySubject}
            onChange={(e) => setGroupBySubject(e.target.checked)}
          />
          <div
            className={styles.toggle}
            role="switch"
            aria-checked={groupBySubject}
            tabIndex={0}
            onClick={() => setGroupBySubject(!groupBySubject)}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                setGroupBySubject(!groupBySubject);
              }
            }}
          />
        </div>
      </div>

      <div className={styles.criteriasSection}>
        <h2 className={styles.sectionTitle}>Criterios disponibles</h2>
        <div className={styles.criteriasList}>{renderContent()}</div>
      </div>

      <ConfirmModal
        open={deleteModalOpen}
        title="Eliminar criterio"
        message={`¿Estás seguro de que deseas eliminar el criterio "${criteriaToDelete?.name}"? Esta acción no se puede deshacer.`}
        onConfirm={handleDeleteConfirm}
        onCancel={() => {
          setDeleteModalOpen(false);
          setCriteriaToDelete(null);
        }}
      />
    </div>
  );
}
