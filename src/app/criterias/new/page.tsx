"use client";

import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { useDomain } from "@/components/Context";
import styles from "./page.module.css";
import { supabase } from "@/utils/supabase/client";

interface Subject {
  id: string;
  name: string;
}

export default function NewCriteriaPage() {
  const router = useRouter();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [subjectId, setSubjectId] = useState("");
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  const { domain } = useDomain();

  useEffect(() => {
    const fetchUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUserId(user?.id ?? null);
    };

    fetchUser();
  }, []);

  useEffect(() => {
    const fetchSubjects = async () => {
      if (!userId) return;

      try {
        const { data, error } = await supabase
          .from("subjects")
          .select("id, name")
          .eq("user_id", userId)
          .order("name", { ascending: true });

        if (error) throw error;

        setSubjects(data);
        if (data.length > 0) {
          setSubjectId(data[0].id);
        }
      } catch (err) {
        console.error("Error fetching subjects:", err);
      }
    };

    fetchSubjects();
  }, [userId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userId) return;

    setLoading(true);
    try {
      await domain.createCriteriaUseCase.execute({
        name,
        description,
        subject_id: subjectId,
        user_id: userId,
      });
      router.push("/criterias");
    } catch (err) {
      console.error("Error creating criteria:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.form}>
      <h1 className={styles.title}>Nuevo criterio de evaluación</h1>
      <form onSubmit={handleSubmit}>
        <div className={styles.formGroup}>
          <label className={styles.label} htmlFor="subject">
            Asignatura
          </label>
          <select
            id="subject"
            value={subjectId}
            onChange={(e) => setSubjectId(e.target.value)}
            className={styles.select}
            required
          >
            {subjects.map((subject) => (
              <option key={subject.id} value={subject.id}>
                {subject.name}
              </option>
            ))}
          </select>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label} htmlFor="name">
            Nombre
          </label>
          <input
            type="text"
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className={styles.input}
            required
          />
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label} htmlFor="description">
            Descripción
          </label>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className={styles.textarea}
            required
          />
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={() => router.back()}
            className={styles.cancelButton}
          >
            Cancelar
          </button>
          <button
            type="submit"
            className={styles.submitButton}
            disabled={loading}
          >
            Crear criterio
          </button>
        </div>
      </form>
    </div>
  );
}
