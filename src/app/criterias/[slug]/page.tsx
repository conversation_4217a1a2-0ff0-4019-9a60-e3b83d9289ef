"use client";

import { useEffect, useState, use } from "react";
import { useRouter } from "next/navigation";
import { useDomain } from "@/components/Context";
import { Criteria, Indicator } from "@/domain/criterias/types";
import { ConfirmModal } from "@/components/ConfirmModal/ConfirmModal";
import styles from "../criterias.module.css";
import { Breadcrumbs } from "@/components/Breadcrumbs/Breadcrumbs";

// Props para la página de criterio
interface CriteriaPageProps {
  readonly params: Promise<{ slug: string }>;
}

export default function CriteriaPage({ params }: CriteriaPageProps) {
  const router = useRouter();
  const { domain } = useDomain();
  const [criteria, setCriteria] = useState<Criteria | null>(null);
  const [indicators, setIndicators] = useState<Indicator[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [indicatorToDelete, setIndicatorToDelete] = useState<Indicator | null>(
    null
  );
  const resolvedParams = use(params);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const criteriaData = await domain.getCriteriaBySlugUseCase.execute(
          resolvedParams.slug
        );
        if (!criteriaData) {
          setError("Criterio no encontrado");
          return;
        }
        setCriteria(criteriaData);
        const indicatorsData = await domain.getIndicatorsUseCase.execute(
          criteriaData.id
        );
        setIndicators(indicatorsData);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Error al cargar los datos"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [resolvedParams.slug, domain]);

  const handleDeleteClick = (indicator: Indicator) => {
    setIndicatorToDelete(indicator);
    setDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!indicatorToDelete) return;

    try {
      await domain.deleteIndicatorUseCase.execute({
        indicatorId: indicatorToDelete.id,
      });
      setIndicators((prev) =>
        prev.filter((ind) => ind.id !== indicatorToDelete.id)
      );
      setDeleteModalOpen(false);
      setIndicatorToDelete(null);
    } catch (error) {
      console.error("Error al eliminar el indicador:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Error al eliminar el indicador"
      );
    }
  };

  if (loading) {
    return <div className={styles.loading}>Cargando...</div>;
  }

  if (error || !criteria) {
    return (
      <div className={styles.error}>{error ?? "Criterio no encontrado"}</div>
    );
  }

  return (
    <div className={styles.container}>
      <Breadcrumbs
        items={[
          { label: "Cursos", href: "/courses" },
          {
            label: criteria.subject?.name ?? "Sin asignatura",
            href: `/subjects/${criteria.subject?.slug ?? ""}`,
          },
          { label: criteria.name },
        ]}
      />

      <div className={styles.subjectDetail}>
        <div className={styles.header}>
          <div className={styles.titleContainer}>
            <h1 className={styles.title}>{criteria.name}</h1>
            <span className={styles.subjectTag}>
              {criteria.subject?.name ?? "Sin asignatura"}
            </span>
          </div>
          <div className={styles.actions}>
            <button
              className="btn-secondary"
              onClick={() => router.push(`/criterias/${criteria.slug}/edit`)}
            >
              Editar
            </button>
          </div>
        </div>

        <div className={styles.content}>
          <div className={styles.section}>
            <h2 className={styles.sectionTitle}>Descripción</h2>
            <p className={styles.description}>{criteria.description}</p>
          </div>

          <div className={styles.section}>
            <div className={styles.sectionHeader}>
              <h2 className={styles.sectionTitle}>Indicadores</h2>
              <button
                onClick={() =>
                  router.push(`/indicators/new?criteria_id=${criteria.id}`)
                }
                className="btn-add btn-icon"
              >
                <span>+</span> Nuevo indicador
              </button>
            </div>

            <div className={styles.indicatorsList}>
              {indicators.length === 0 ? (
                <p className={styles.emptyMessage}>No hay indicadores</p>
              ) : (
                indicators.map((indicator) => (
                  <div key={indicator.id} className={styles.indicatorItem}>
                    <div className={styles.indicatorHeader}>
                      <h3 className={styles.indicatorTitle}>
                        {indicator.title}
                      </h3>
                      <button
                        className="btn-close"
                        onClick={() => handleDeleteClick(indicator)}
                        aria-label={`Eliminar indicador ${indicator.title}`}
                      >
                        <svg
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"
                            fill="currentColor"
                          />
                        </svg>
                      </button>
                    </div>
                    <div className={styles.indicatorActions}>
                      <button
                        onClick={() =>
                          router.push(`/indicators/${indicator.slug}`)
                        }
                        className="btn-primary"
                      >
                        Ver detalle
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        <ConfirmModal
          open={deleteModalOpen}
          onCancel={() => {
            setDeleteModalOpen(false);
            setIndicatorToDelete(null);
          }}
          onConfirm={handleDeleteConfirm}
          title="Eliminar indicador"
          message={`¿Estás seguro de que deseas eliminar el indicador "${indicatorToDelete?.title}"? Esta acción no se puede deshacer.`}
        />
      </div>
    </div>
  );
}
