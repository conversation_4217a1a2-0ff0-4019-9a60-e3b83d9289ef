.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.title {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: var(--title-color);
}

.form {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.formGroup input,
.formGroup textarea,
.formGroup select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.formGroup textarea {
  min-height: 150px;
  resize: vertical;
}

.formGroup select {
  background-color: var(--bg-primary);
  cursor: pointer;
}

.formGroup select:focus {
  outline: none;
  border-color: #0070f3;
  box-shadow: 0 0 0 2px rgba(0, 112, 243, 0.1);
}

.buttonGroup {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.submitButton,
.cancelButton {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submitButton {
  background-color: #0070f3;
  color: white;
  border: none;
}

.submitButton:hover {
  background-color: #0051b3;
}

.cancelButton {
  background-color: #f5f5f5;
  color: var(--title-color);
  border: 1px solid #ddd;
}

.cancelButton:hover {
  background-color: #e5e5e5;
}

.loading {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
  color: var(--title-color);
}

.error {
  text-align: center;
  padding: 2rem;
  color: #dc2626;
  font-size: 1.2rem;
}

.deleteSection {
  margin-top: 3rem;
  padding: 2rem;
  border: 1px solid #dc2626;
  border-radius: 8px;
  background-color: #fef2f2;
}

.deleteTitle {
  color: #dc2626;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.deleteDescription {
  color: var(--title-color);
  margin-bottom: 1.5rem;
}

.deleteButton {
  background-color: #dc2626;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.deleteButton:hover {
  background-color: #b91c1c;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: 8px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal h2 {
  color: #dc2626;
  margin-bottom: 1rem;
}

.modal p {
  color: var(--title-color);
  margin-bottom: 1.5rem;
}

.modalButtons {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.confirmDeleteButton {
  background-color: #dc2626;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.confirmDeleteButton:hover {
  background-color: #b91c1c;
}
