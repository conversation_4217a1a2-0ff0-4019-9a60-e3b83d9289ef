"use client";

import { useEffect, useState, use } from "react";
import { useRouter } from "next/navigation";
import { useDomain } from "@/components/Context";
import { Criteria } from "@/domain/criterias/types";
import styles from "./edit.module.css";

// Props para la página de edición de criterio
interface EditCriteriaPageProps {
  params: Promise<{ slug: string }>;
}

interface Subject {
  id: string;
  name: string;
  slug: string;
}

export default function EditCriteriaPage({
  params,
}: Readonly<EditCriteriaPageProps>) {
  const router = useRouter();
  const { domain } = useDomain();
  const [criteria, setCriteria] = useState<Criteria | null>(null);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    subject_id: "",
  });
  const resolvedParams = use(params);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Obtener el criterio
        const criteriaData = await domain.getCriteriaBySlugUseCase.execute(
          resolvedParams.slug
        );

        if (!criteriaData) {
          setError("Criterio no encontrado");
          return;
        }

        // Obtener la lista de asignaturas
        const subjectsData = await domain.getSubjectsUseCase.execute();
        setSubjects(subjectsData);

        setCriteria(criteriaData);
        setFormData({
          name: criteriaData.name,
          description: criteriaData.description,
          subject_id: criteriaData.subject_id,
        });
      } catch (err) {
        console.error("Error fetching data:", err);
        setError(
          err instanceof Error ? err.message : "Error al cargar los datos"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [resolvedParams.slug, domain]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!criteria) return;

    try {
      const updatedCriteria = await domain.updateCriteriaUseCase.execute({
        id: criteria.id,
        ...formData,
      });

      router.push(`/criterias/${updatedCriteria.slug}`);
    } catch (err) {
      console.error("Error updating criteria:", err);
      setError(
        err instanceof Error ? err.message : "Error al actualizar el criterio"
      );
    }
  };

  const handleDelete = async () => {
    if (!criteria) return;

    try {
      await domain.deleteCriteriaUseCase.execute(criteria.id);
      router.push("/criterias");
    } catch (err) {
      console.error("Error deleting criteria:", err);
      setError(
        err instanceof Error ? err.message : "Error al eliminar el criterio"
      );
    }
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  if (loading) {
    return <div className={styles.loading}>Cargando...</div>;
  }

  if (error || !criteria) {
    return (
      <div className={styles.error}>{error ?? "Criterio no encontrado"}</div>
    );
  }

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Editar Criterio</h1>
      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formGroup}>
          <label htmlFor="name">Nombre</label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
          />
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="description">Descripción</label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            required
          />
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="subject_id">Asignatura</label>
          <select
            id="subject_id"
            name="subject_id"
            value={formData.subject_id}
            onChange={handleChange}
            required
            className={styles.select}
          >
            <option value="">Selecciona una asignatura</option>
            {subjects.map((subject) => (
              <option key={subject.id} value={subject.id}>
                {subject.name}
              </option>
            ))}
          </select>
        </div>

        <div className={styles.buttonGroup}>
          <button type="submit" className={styles.submitButton}>
            Guardar Cambios
          </button>
          <button
            type="button"
            onClick={() => router.push(`/criterias/${resolvedParams.slug}`)}
            className={styles.cancelButton}
          >
            Cancelar
          </button>
        </div>
      </form>

      <div className={styles.deleteSection}>
        <h2 className={styles.deleteTitle}>Zona de Peligro</h2>
        <p className={styles.deleteDescription}>
          Una vez que elimines un criterio, no hay vuelta atrás. Por favor, ten
          cuidado.
        </p>
        <button
          type="button"
          onClick={() => setShowDeleteConfirm(true)}
          className={styles.deleteButton}
        >
          Eliminar Criterio
        </button>
      </div>

      {showDeleteConfirm && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <h2>¿Estás seguro?</h2>
            <p>
              Esta acción no se puede deshacer. ¿Realmente deseas eliminar este
              criterio?
            </p>
            <div className={styles.modalButtons}>
              <button
                onClick={handleDelete}
                className={styles.confirmDeleteButton}
              >
                Sí, eliminar
              </button>
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className={styles.cancelButton}
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
