/* Container principal */
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
  padding: 2rem;
}

/* Hero Section */
.heroSection {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.heroContent {
  flex: 1;
  max-width: 600px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--title-color);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.heroImageContainer {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.heroImage {
  max-width: 100%;
  height: auto;
}

/* Create But<PERSON> */
.createButton {
  padding: 0.75rem 1.5rem;
  background: #0284c7; /* Color sólido más oscuro para mejor contraste */
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 700; /* Aumentado el peso de la fuente */
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(29, 136, 175, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* Sombra más pronunciada */
  letter-spacing: 0.5px; /* Mejor legibilidad */
}

.createButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(29, 136, 175, 0.3);
  background: #0369a1; /* Color hover más oscuro */
}

/* Filters Section */
.filtersSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem 1.5rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.searchContainer {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.searchInput {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  padding: 0.5rem 2.2rem;
  border: 1px solid var(--outline-color);
  border-radius: 6px;
  font-size: 0.875rem;
  width: 300px;
  transition: all 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(29, 136, 175, 0.1);
}

.searchInput::placeholder {
  color: var(--text-secondary);
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

/* Group Toggle */
.groupToggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: var(--bg-primary);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.groupToggle label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--title-color);
  cursor: pointer;
  user-select: none;
}

.groupToggle input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.groupToggle .toggle {
  position: relative;
  width: 44px;
  height: 24px;
  background-color: var(--border-color);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.groupToggle .toggle:before {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  background-color: var(--bg-primary);
  transition: transform 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.groupToggle input[type="checkbox"]:checked + .toggle {
  background-color: var(--primary);
}

.groupToggle input[type="checkbox"]:checked + .toggle:before {
  transform: translateX(20px);
}

/* Criteria Detail Page Specific Styles */
.subjectDetail {
  margin-top: 1.5rem;
}

.titleContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.descriptionCard {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 1rem;
}

.indicatorsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
  margin-top: 1rem;
}

.indicatorItem {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--bg-primary);
  border: 1px solid #e0e0e0;
  border-radius: 0 0 12px 12px;
  width: 100%;
  position: relative;
}

.indicatorHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.addIndicatorButton {
  width: auto;
  padding: 0.5rem 1rem;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 0;
}

.addIndicatorButton:hover {
  background-color: #388e3c;
}

/* Header y navegación */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background-color: var(--bg-primary);
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid var(--outline-color);
}

.title {
  font-size: 2rem;
  color: var(--title-color);
  margin: 0;
}

.subjectTag {
  padding: 0.25rem 0.75rem;
  background-color: rgba(29, 136, 175, 0.1);
  color: var(--primary);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  max-width: fit-content;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.actions {
  display: flex;
  gap: 1rem;
}

/* Secciones */
.content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section {
  background-color: #f9f9f9;
  padding: 1.5rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
}

/* Criterias Section */
.criteriasSection {
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--title-color);
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--border-color);
}

/* Lista de criterios */
.criteriasList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Grid de criterios */
.criteriaGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  width: 100%;
}

/* Tarjeta de criterio */
.criteriaCard {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.criteriaCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.criteriaHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  position: relative;
}

.closeButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  opacity: 0.7;
}

.closeButton:hover {
  background-color: rgba(211, 47, 47, 0.1);
  color: #d32f2f;
  opacity: 1;
}

.criteriaContent {
  flex: 1;
  padding: 1.25rem;
}

.criteriaFooter {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
}

.criteriaActions {
  display: flex;
  gap: 0.5rem;
}

/* Botones modernos */
.detailButton {
  padding: 0.75rem 1.25rem;
  background: #0284c7; /* Color sólido más oscuro para mejor contraste */
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 700; /* Aumentado el peso de la fuente */
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(29, 136, 175, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* Sombra más pronunciada */
  letter-spacing: 0.5px; /* Mejor legibilidad */
}

.detailButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(29, 136, 175, 0.3);
  background: #0369a1; /* Color hover más oscuro */
}

.deleteButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: rgba(211, 47, 47, 0.1);
  color: #d32f2f;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.deleteButton:hover {
  background-color: rgba(211, 47, 47, 0.2);
  transform: translateY(-2px);
}

/* Estados */
.loadingState,
.errorState,
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(29, 136, 175, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.errorMessage {
  color: #d32f2f;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.retryButton {
  padding: 0.75rem 1.5rem;
  background-color: #d32f2f;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retryButton:hover {
  background-color: #b71c1c;
  transform: translateY(-2px);
}

.emptyStateIcon {
  margin-bottom: 1.5rem;
  opacity: 0.6;
}

.emptyMessage {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 0.5rem;
}

.emptyDescription {
  color: var(--text-secondary);
  max-width: 400px;
  line-height: 1.5;
}

/* Agrupación por asignatura */
.groupedList {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.subjectGroup {
  background-color: var(--bg-primary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.subjectTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
  padding: 1.25rem 1.5rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.criteriaItem {
  background-color: var(--bg-primary);
  border: 1px solid var(--outline-color);
  border-radius: 0.5rem;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.criteriaItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.criteriaInfo {
  flex: 1;
  position: relative;
}

.criteriaTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0.5rem 0;
  color: var(--title-color);
}

.criteriaDescription {
  color: #4a5568;
  margin: 0;
}

/* Lista de indicadores */
.indicatorsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: var(--bg-primary);
  border-radius: 0.5rem;
  border: 1px solid #e0e0e0;
  margin-bottom: 0.5rem;
}

.indicatorTitle {
  color: var(--title-color);
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.indicatorActions {
  display: flex;
  gap: 0.75rem;
  margin-top: auto;
}

/* Botones */
.editButton,
.detailButton,
.deleteButton {
  padding: 0.75rem 1.25rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
}

.editButton {
  padding: 0.5rem 1rem;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.editButton:hover {
  background-color: #1976d2;
}

.detailButton {
  padding: 0.5rem 1rem;
  background-color: var(--bg-primary);
  color: #1976d2;
  border: 1px solid #1976d2;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.detailButton:hover {
  background-color: #1976d2;
  color: white;
}

.deleteButton {
  padding: 0.5rem 1rem;
  background-color: var(--bg-primary);
  color: #d32f2f;
  border: 1px solid #d32f2f;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 100px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.5rem;
}

.deleteButton:hover {
  background-color: #d32f2f;
  color: white;
}

.addButton {
  padding: 0.5rem 1rem;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.addButton:hover {
  background-color: #388e3c;
}

.addButtonContainer {
  margin-top: 1rem;
}

/* Estados */
.loading,
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.125rem;
  color: #4a4a4a;
}

.error {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 1rem;
  border-radius: 0.375rem;
  margin: 2rem auto;
  max-width: 600px;
}

.emptyMessage {
  text-align: center;
  color: #9e9e9e;
  font-size: 1rem;
  padding: 2rem;
  background-color: #f5f5f5;
  border-radius: 0.375rem;
}

/* Agrupación por asignatura */
.subjectGroup {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.subjectTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.subjectCriterias {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Búsqueda y filtros */
.headerActions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.groupToggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: var(--bg-primary);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--outline-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.groupToggle span {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--title-color);
  cursor: pointer;
  user-select: none;
}

/* Switch para agrupación */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e2e8f0;
  transition: 0.4s;
  border-radius: 12px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background-color: var(--bg-primary);
  transition: 0.4s;
  border-radius: 50%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

input:checked + .slider {
  background-color: #3b82f6;
}

input:checked + .slider:before {
  transform: translateX(20px);
}
