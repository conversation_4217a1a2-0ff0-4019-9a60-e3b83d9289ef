/* Container principal */
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
  padding: 2rem;
}

/* Hero Section */
.heroSection {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.heroContent {
  flex: 1;
  max-width: 600px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--title-color);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.heroImageContainer {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.heroImage {
  max-width: 100%;
  height: auto;
}

/* Filters Section */
.filtersSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem 1.5rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.searchContainer {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.searchInput {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  padding: 0.5rem 2.2rem;
  border: 1px solid var(--outline-color);
  border-radius: 6px;
  font-size: 0.875rem;
  width: 300px;
  transition: all 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(29, 136, 175, 0.1);
}

.searchInput::placeholder {
  color: var(--text-secondary);
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.groupToggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: var(--bg-primary);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.groupToggle label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--title-color);
  cursor: pointer;
  user-select: none;
}

.groupToggle input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.groupToggle .toggle {
  position: relative;
  width: 44px;
  height: 24px;
  background-color: var(--border-color);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.groupToggle .toggle:before {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  background-color: var(--bg-primary);
  transition: transform 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.groupToggle input[type="checkbox"]:checked + .toggle {
  background-color: var(--primary);
}

.groupToggle input[type="checkbox"]:checked + .toggle:before {
  transform: translateX(20px);
}

/* Indicators Section */
.indicatorsSection {
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--title-color);
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--border-color);
}

.indicatorsList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  width: 100%;
}

.indicatorItem {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0 0 12px 12px;
  padding: 0;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: visible; /* Cambiado de hidden a visible para que el botón de cierre sea visible */
  height: 100%;
  z-index: 1; /* Asegura que la tarjeta tenga un z-index adecuado */
}

.indicatorItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.indicatorHeader {
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  position: relative;
}

.indicatorContent {
  padding: 1.25rem;
  flex: 1;
}

.indicatorFooter {
  padding: 1.25rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
}

.indicatorActions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.indicatorTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--title-color);
  padding-right: 2rem;
}

.badgesContainer {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.criteriaTag,
.subjectTag {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  max-width: fit-content;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.criteriaTag {
  background-color: rgba(29, 136, 175, 0.1);
  color: var(--primary);
}

.subjectTag {
  background-color: rgba(123, 31, 162, 0.1);
  color: #7b1fa2;
}

.levelsList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
}

.levelBadge {
  padding: 0.75rem 1rem;
  background-color: rgba(29, 136, 175, 0.1);
  color: var(--primary);
  border-radius: 6px;
  font-size: 0.9rem;
  text-align: left;
  transition: background-color 0.2s ease;
}

.levelBadge:hover {
  background-color: rgba(29, 136, 175, 0.2);
}

.criteriaGroup {
  background-color: var(--bg-primary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.criteriaTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
  padding: 1.25rem 1.5rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.criteriaIndicators {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
}

.detailButton {
  padding: 0.5rem 1rem;
  background-color: var(--bg-primary);
  color: #1976d2;
  border: 1px solid #1976d2;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 100px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.detailButton:hover {
  background-color: #1976d2;
  color: white;
}

/* Create Button */
.createButton {
  padding: 0.75rem 1.5rem;
  background: #0284c7; /* Color sólido más oscuro para mejor contraste */
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 700; /* Aumentado el peso de la fuente */
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(29, 136, 175, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* Sombra más pronunciada */
  letter-spacing: 0.5px; /* Mejor legibilidad */
}

.createButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(29, 136, 175, 0.3);
  background: #0369a1; /* Color hover más oscuro */
}

.addButton {
  padding: 0.5rem 1rem;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.addButton:hover {
  background-color: #388e3c;
}

.addButtonContainer {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}

.loading,
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.125rem;
  color: #4a4a4a;
}

.error {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 1rem;
  border-radius: 0.375rem;
  margin: 2rem auto;
  max-width: 600px;
}

.emptyMessage {
  text-align: center;
  color: #9e9e9e;
  font-size: 1rem;
  padding: 2rem;
  background-color: #f5f5f5;
  border-radius: 0.375rem;
}

.deleteButton {
  padding: 0.5rem 1rem;
  background-color: var(--bg-primary);
  color: #d32f2f;
  border: 1px solid #d32f2f;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 100px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.5rem;
}

.deleteButton:hover {
  background-color: #d32f2f;
  color: white;
}
