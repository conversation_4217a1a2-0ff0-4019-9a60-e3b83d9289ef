"use client";

import { useEffect, useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useDomain } from "@/components/Context";
import { Indicator } from "@/domain/indicators/types";
import styles from "./indicators.module.css";
import { supabase } from "@/utils/supabase/client";
import { ConfirmModal } from "@/components/ConfirmModal/ConfirmModal";
import { CloseIcon, SearchIcon } from "@/components/icons";
import { HeroSection, HeroVariant } from "@/components/HeroSection";

interface IndicatorWithCriteria extends Omit<Indicator, "criteria_id"> {
  criteria: {
    id: string;
    name: string;
    slug: string;
    subject: {
      id: string;
      name: string;
      slug: string;
      user_id: string;
    };
  };
}

type GroupedIndicators = [string, IndicatorWithCriteria[]][];

export default function IndicatorsPage() {
  const router = useRouter();
  const [indicators, setIndicators] = useState<IndicatorWithCriteria[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState("");
  const [userId, setUserId] = useState<string | null>(null);
  const [groupByCriteria, setGroupByCriteria] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [indicatorToDelete, setIndicatorToDelete] =
    useState<IndicatorWithCriteria | null>(null);
  const { domain } = useDomain();

  useEffect(() => {
    const fetchUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUserId(user?.id ?? null);
    };

    fetchUser();
  }, []);

  useEffect(() => {
    const fetchIndicators = async () => {
      if (!userId) return;

      try {
        const { data, error } = await supabase
          .from("indicators")
          .select(
            `
            id,
            title,
            slug,
            levels:indicator_levels (
              id,
              level_number,
              title,
              created_at,
              updated_at
            ),
            criteria:criteria_id (
              id,
              name,
              slug,
              subject:subject_id (
                id,
                name,
                slug,
                user_id
              )
            ),
            created_at,
            updated_at
          `
          )
          .eq("criteria.subject.user_id", userId)
          .order("created_at", { ascending: false });

        if (error) throw error;

        setIndicators(data as unknown as IndicatorWithCriteria[]);
      } catch (err) {
        console.error("Error fetching indicators:", err);
        setError("Error al cargar los indicadores");
      } finally {
        setLoading(false);
      }
    };

    fetchIndicators();
  }, [userId]);

  const filteredAndGroupedIndicators = useMemo(() => {
    let filtered = indicators;

    if (search) {
      const searchLower = search.toLowerCase();
      filtered = indicators.filter(
        (indicator) =>
          indicator.title.toLowerCase().includes(searchLower) ||
          indicator.criteria.name.toLowerCase().includes(searchLower) ||
          indicator.criteria.subject.name.toLowerCase().includes(searchLower)
      );
    }

    if (!groupByCriteria) {
      return filtered;
    }

    const grouped = filtered.reduce<Record<string, IndicatorWithCriteria[]>>(
      (acc, indicator) => {
        const criteriaName = indicator.criteria.name;
        if (!acc[criteriaName]) {
          acc[criteriaName] = [];
        }
        acc[criteriaName].push(indicator);
        return acc;
      },
      {}
    );

    return Object.entries(grouped).sort(([a], [b]) => a.localeCompare(b));
  }, [indicators, search, groupByCriteria]) as
    | IndicatorWithCriteria[]
    | GroupedIndicators;

  const handleDeleteClick = (indicator: IndicatorWithCriteria) => {
    setIndicatorToDelete(indicator);
    setDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!indicatorToDelete) return;

    try {
      await domain.deleteIndicatorUseCase.execute({
        indicatorId: indicatorToDelete.id,
      });

      // Update the indicators list by removing the deleted indicator
      setIndicators((prev) =>
        prev.filter((ind) => ind.id !== indicatorToDelete.id)
      );

      setDeleteModalOpen(false);
      setIndicatorToDelete(null);
    } catch (error) {
      console.error("Error al eliminar el indicador:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Error al eliminar el indicador"
      );
    }
  };

  const renderContent = () => {
    if (loading) {
      return <p>Cargando...</p>;
    }

    if (error) {
      return <p className={styles.error}>{error}</p>;
    }

    if (!groupByCriteria) {
      // Vista sin agrupar
      if (
        (filteredAndGroupedIndicators as IndicatorWithCriteria[]).length === 0
      ) {
        return (
          <p className={styles.emptyMessage}>No hay indicadores disponibles</p>
        );
      }

      return (filteredAndGroupedIndicators as IndicatorWithCriteria[]).map(
        (indicator) => (
          <div key={indicator.id} className={styles.indicatorItem}>
            <button
              onClick={() => handleDeleteClick(indicator)}
              className="btn-close"
              aria-label={`Eliminar indicador ${indicator.title}`}
            >
              <CloseIcon />
            </button>

            <div className={styles.indicatorHeader}>
              <div className={styles.badgesContainer}>
                <div className={styles.criteriaTag}>
                  {indicator.criteria.name}
                </div>
                <div className={styles.subjectTag}>
                  {indicator.criteria.subject.name}
                </div>
              </div>
              <h3 className={styles.indicatorTitle}>{indicator.title}</h3>
            </div>

            <div className={styles.indicatorContent}>
              <div className={styles.levelsList}>
                {indicator.levels?.map((level) => (
                  <span key={level.id} className={styles.levelBadge}>
                    Nivel {level.level_number}: {level.title}
                  </span>
                ))}
              </div>
            </div>

            <div className={styles.indicatorFooter}>
              <button
                onClick={() => router.push(`/indicators/${indicator.slug}`)}
                className="btn-primary"
              >
                Ver detalles
              </button>
            </div>
          </div>
        )
      );
    }

    // Vista agrupada
    if ((filteredAndGroupedIndicators as GroupedIndicators).length === 0) {
      return (
        <p className={styles.emptyMessage}>No hay indicadores disponibles</p>
      );
    }

    return (filteredAndGroupedIndicators as GroupedIndicators).map(
      ([criteriaName, criteriaIndicators]) => (
        <div key={criteriaName} className={styles.criteriaGroup}>
          <h2 className={styles.criteriaTitle}>{criteriaName}</h2>
          <div className={styles.criteriaIndicators}>
            {criteriaIndicators.map((indicator) => (
              <div key={indicator.id} className={styles.indicatorItem}>
                <button
                  onClick={() => handleDeleteClick(indicator)}
                  className="btn-close"
                  aria-label={`Eliminar indicador ${indicator.title}`}
                >
                  <CloseIcon />
                </button>

                <div className={styles.indicatorHeader}>
                  <div className={styles.badgesContainer}>
                    <div className={styles.subjectTag}>
                      {indicator.criteria.subject.name}
                    </div>
                  </div>
                  <h3 className={styles.indicatorTitle}>{indicator.title}</h3>
                </div>

                <div className={styles.indicatorContent}>
                  <div className={styles.levelsList}>
                    {indicator.levels?.map((level) => (
                      <span key={level.id} className={styles.levelBadge}>
                        Nivel {level.level_number}: {level.title}
                      </span>
                    ))}
                  </div>
                </div>

                <div className={styles.indicatorFooter}>
                  <button
                    onClick={() => router.push(`/indicators/${indicator.slug}`)}
                    className="btn-primary"
                  >
                    Ver detalles
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )
    );
  };

  return (
    <div className={styles.container}>
      <HeroSection
        title="Indicadores de evaluación"
        description="Los indicadores te permiten definir niveles específicos de desempeño para cada criterio de evaluación. Crea, gestiona y organiza tus indicadores para evaluar el progreso de tus estudiantes de manera efectiva."
        ctaText="Crear nuevo indicador"
        ctaUrl="/indicators/new"
        variant={HeroVariant.INDICATORS}
      />

      <div className={styles.filtersSection}>
        <div className={styles.searchContainer}>
          <input
            type="text"
            placeholder="Buscar indicadores..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className={styles.searchInput}
          />
          <SearchIcon
            className={styles.searchIcon}
            style={{ color: "var(--text-secondary)" }}
          />
        </div>
        <div className={styles.groupToggle}>
          <label htmlFor="groupToggle">Agrupar por criterio</label>
          <input
            id="groupToggle"
            type="checkbox"
            checked={groupByCriteria}
            onChange={(e) => setGroupByCriteria(e.target.checked)}
          />
          <div
            className={styles.toggle}
            role="switch"
            aria-checked={groupByCriteria}
            tabIndex={0}
            onClick={() => setGroupByCriteria(!groupByCriteria)}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                setGroupByCriteria(!groupByCriteria);
              }
            }}
          />
        </div>
      </div>

      <div className={styles.indicatorsSection}>
        <h2 className={styles.sectionTitle}>Indicadores disponibles</h2>
        <div className={styles.indicatorsList}>{renderContent()}</div>
      </div>

      <div className={styles.addButtonContainer}>
        <button
          onClick={() => router.push("/indicators/new")}
          className="btn-add btn-icon"
        >
          <span>+</span> Añadir indicador
        </button>
      </div>

      <ConfirmModal
        open={deleteModalOpen}
        title="Eliminar indicador"
        message={`¿Estás seguro de que deseas eliminar el indicador "${indicatorToDelete?.title}"? Esta acción no se puede deshacer.`}
        onConfirm={handleDeleteConfirm}
        onCancel={() => {
          setDeleteModalOpen(false);
          setIndicatorToDelete(null);
        }}
      />
    </div>
  );
}
