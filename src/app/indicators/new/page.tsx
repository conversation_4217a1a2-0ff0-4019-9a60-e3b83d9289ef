"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useDomain } from "@/components/Context";
import { supabase } from "@/utils/supabase/client";
import styles from "./new.module.css";

interface Level {
  level_number: number;
  title: string;
}

interface Criteria {
  id: string;
  name: string;
  subject: {
    id: string;
    name: string;
  };
}

export default function NewIndicatorPage() {
  const router = useRouter();
  const { domain } = useDomain();
  const [title, setTitle] = useState("");
  const [selectedCriteriaId, setSelectedCriteriaId] = useState<string>("");
  const [criterias, setCriterias] = useState<Criteria[]>([]);
  const [levels, setLevels] = useState<Level[]>([
    { level_number: 1, title: "Nivel uno" },
    { level_number: 2, title: "Nivel dos" },
    { level_number: 3, title: "Nivel tres" },
    { level_number: 4, title: "Nivel cuatro" },
  ]);
  const [selectedLevel, setSelectedLevel] = useState<number>(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCriterias = async () => {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) {
          setError("No se encontró el usuario");
          return;
        }

        const criterias = await domain.getCriteriasUseCase.execute(user.id);
        setCriterias(criterias);
      } catch (error) {
        console.error("Error al obtener los criterios:", error);
        setError("Error al cargar los criterios");
      }
    };

    fetchCriterias();
  }, [domain]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedCriteriaId) {
      setError("Debes seleccionar un criterio");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await domain.createIndicatorUseCase.execute({
        title,
        criteria_id: selectedCriteriaId,
        levels,
      });
      router.push("/indicators");
    } catch (error) {
      console.error("Error al crear el indicador:", error);
      setError(
        error instanceof Error ? error.message : "Error al crear el indicador"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleLevelChange = (levelNumber: number, newTitle: string) => {
    setLevels((prevLevels) =>
      prevLevels.map((level) =>
        level.level_number === levelNumber
          ? { ...level, title: newTitle }
          : level
      )
    );
  };

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Nuevo indicador</h1>

      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formGroup}>
          <label htmlFor="title" className={styles.label}>
            Título
          </label>
          <input
            type="text"
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className={styles.input}
            required
          />
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="criteria" className={styles.label}>
            Criterio
          </label>
          <select
            id="criteria"
            value={selectedCriteriaId}
            onChange={(e) => setSelectedCriteriaId(e.target.value)}
            className={styles.input}
            required
          >
            <option value="">Selecciona un criterio</option>
            {criterias.map((criteria) => (
              <option key={criteria.id} value={criteria.id}>
                {criteria.name} - {criteria.subject.name}
              </option>
            ))}
          </select>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Niveles</label>
          <div className={styles.levels}>
            {levels.map((level) => (
              <div key={level.level_number} className={styles.level}>
                <div className={styles.levelHeader}>
                  <input
                    type="radio"
                    id={`level-${level.level_number}`}
                    name="selectedLevel"
                    value={level.level_number}
                    checked={selectedLevel === level.level_number}
                    onChange={(e) => setSelectedLevel(Number(e.target.value))}
                    className={styles.radio}
                  />
                  <label
                    htmlFor={`level-${level.level_number}`}
                    className={styles.levelLabel}
                  >
                    Nivel {level.level_number}
                  </label>
                </div>
                <input
                  type="text"
                  value={level.title}
                  onChange={(e) =>
                    handleLevelChange(level.level_number, e.target.value)
                  }
                  className={styles.levelInput}
                  required
                />
              </div>
            ))}
          </div>
        </div>

        {error && <div className={styles.error}>{error}</div>}

        <div className={styles.actions}>
          <button
            type="button"
            onClick={() => router.back()}
            className={styles.cancelButton}
          >
            Cancelar
          </button>
          <button
            type="submit"
            className={styles.submitButton}
            disabled={loading}
          >
            {loading ? "Creando..." : "Crear indicador"}
          </button>
        </div>
      </form>
    </div>
  );
}
