.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 2rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-size: 1rem;
  font-weight: 500;
  color: #4a4a4a;
}

.input {
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: #1976d2;
}

.levels {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.level {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.levelHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.radio {
  width: 1.25rem;
  height: 1.25rem;
  margin: 0;
  cursor: pointer;
}

.levelLabel {
  font-weight: 500;
  color: var(--title-color);
  cursor: pointer;
}

.levelInput {
  padding: 0.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.levelInput:focus {
  outline: none;
  border-color: #1976d2;
}

.error {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.cancelButton,
.submitButton {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background-color: #f5f5f5;
  color: var(--title-color);
  border: 1px solid #e0e0e0;
}

.cancelButton:hover {
  background-color: #e0e0e0;
}

.submitButton {
  background-color: #1976d2;
  color: white;
  border: none;
}

.submitButton:hover {
  background-color: #1565c0;
}

.submitButton:disabled {
  background-color: #bdbdbd;
  cursor: not-allowed;
}
