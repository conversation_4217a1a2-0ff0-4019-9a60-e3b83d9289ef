.container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 2rem;
  color: var(--title-color);
  margin: 0;
}

.actions {
  display: flex;
  gap: 1rem;
}

.backButton {
  padding: 0.5rem 1rem;
  background-color: #f5f5f5;
  color: var(--title-color);
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.backButton:hover {
  background-color: #e0e0e0;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section {
  background-color: #f9f9f9;
  padding: 1.5rem;
  border-radius: 4px;
}

.sectionTitle {
  font-size: 1.25rem;
  color: var(--title-color);
  margin: 0 0 1rem 0;
}

.levels {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.level {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--bg-primary);
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.levelNumber {
  font-weight: 600;
  color: #1976d2;
  min-width: 5rem;
}

.levelTitle {
  color: #4a4a4a;
  font-size: 1rem;
}

.info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info p {
  margin: 0;
  color: #4a4a4a;
}

.info strong {
  color: var(--title-color);
}

.loading,
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.125rem;
  color: #4a4a4a;
}

.error {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 1rem;
  border-radius: 4px;
  margin: 2rem auto;
  max-width: 600px;
}

.editForm {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.titleInput {
  font-size: 2rem;
  color: var(--title-color);
  padding: 0.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  width: 100%;
}

.editActions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.saveButton {
  padding: 0.5rem 1rem;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.saveButton:hover {
  background-color: #1976d2;
}

.cancelButton {
  padding: 0.5rem 1rem;
  background-color: #f5f5f5;
  color: var(--title-color);
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton:hover {
  background-color: #e0e0e0;
}

.editButton {
  padding: 0.5rem 1rem;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.editButton:hover {
  background-color: #1976d2;
}

.levelInput {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.levelInput:focus {
  outline: none;
  border-color: #1976d2;
}
