"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { useDomain } from "@/components/Context";
import { Indicator } from "@/domain/indicators/types";
import styles from "./indicator.module.css";
import { Breadcrumbs } from "@/components/Breadcrumbs/Breadcrumbs";

export default function IndicatorDetailPage() {
  const { domain } = useDomain();
  const params = useParams();
  const [indicator, setIndicator] = useState<Indicator | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState("");
  const [editedLevels, setEditedLevels] = useState<
    Array<{ id?: string; level_number: number; title: string }>
  >([]);

  useEffect(() => {
    const fetchIndicator = async () => {
      try {
        const slug = params.slug as string;
        const indicatorData = await domain.getIndicatorBySlugUseCase.execute(
          slug
        );

        if (!indicatorData) {
          setError("Indicador no encontrado");
          return;
        }

        setIndicator(indicatorData);
        setEditedTitle(indicatorData.title);
        setEditedLevels(indicatorData.levels);
      } catch (error) {
        console.error("Error al obtener el indicador:", error);
        setError(
          error instanceof Error
            ? error.message
            : "Error al obtener el indicador"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchIndicator();
  }, [params.slug]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    if (indicator) {
      setEditedTitle(indicator.title);
      setEditedLevels(indicator.levels);
    }
    setIsEditing(false);
  };

  const handleLevelChange = (levelNumber: number, newTitle: string) => {
    setEditedLevels((prevLevels) =>
      prevLevels.map((level) =>
        level.level_number === levelNumber
          ? { ...level, title: newTitle }
          : level
      )
    );
  };

  const handleSave = async () => {
    if (!indicator) return;

    try {
      const updatedIndicator = await domain.updateIndicatorUseCase.execute({
        id: indicator.id,
        title: editedTitle,
        slug: indicator.slug,
        levels: editedLevels,
      });

      setIndicator(updatedIndicator);
      setIsEditing(false);
    } catch (error) {
      console.error("Error al actualizar el indicador:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Error al actualizar el indicador"
      );
    }
  };

  if (loading) {
    return <div className={styles.loading}>Cargando...</div>;
  }

  if (error) {
    return <div className={styles.error}>{error}</div>;
  }

  if (!indicator) {
    return <div className={styles.error}>Indicador no encontrado</div>;
  }

  // Prepare breadcrumb items with proper checks to avoid undefined errors
  const breadcrumbItems = [{ label: "Cursos", href: "/courses" }];

  // Only add subject if it exists
  if (indicator.criteria?.subject) {
    breadcrumbItems.push({
      label: indicator.criteria.subject.name,
      href: `/subjects/${indicator.criteria.subject.slug}`,
    });
  }

  // Add criteria if it exists
  if (indicator.criteria?.name) {
    breadcrumbItems.push({
      label: indicator.criteria.name,
      href: `/criterias/${indicator.criteria.slug}`,
    });
  }

  // Always add the indicator title
  breadcrumbItems.push({
    label: indicator.title,
    href: `/indicators/${indicator.slug}`,
  });

  return (
    <div className={styles.container}>
      <Breadcrumbs items={breadcrumbItems} />
      <div className={styles.indicatorDetail}>
        <div className={styles.header}>
          {isEditing ? (
            <div className={styles.editForm}>
              <input
                type="text"
                value={editedTitle}
                onChange={(e) => setEditedTitle(e.target.value)}
                className={styles.titleInput}
              />
              <div className={styles.editActions}>
                <button onClick={handleSave} className={styles.saveButton}>
                  Guardar
                </button>
                <button onClick={handleCancel} className={styles.cancelButton}>
                  Cancelar
                </button>
              </div>
            </div>
          ) : (
            <>
              <h1 className={styles.title}>{indicator.title}</h1>
              <div className={styles.actions}>
                <button onClick={handleEdit} className={styles.editButton}>
                  Editar
                </button>
              </div>
            </>
          )}
        </div>

        <div className={styles.content}>
          <div className={styles.section}>
            <h2 className={styles.sectionTitle}>Niveles</h2>
            <div className={styles.levels}>
              {isEditing
                ? editedLevels.map((level) => (
                    <div key={level.level_number} className={styles.level}>
                      <span className={styles.levelNumber}>
                        Nivel {level.level_number}
                      </span>
                      <input
                        type="text"
                        value={level.title}
                        onChange={(e) =>
                          handleLevelChange(level.level_number, e.target.value)
                        }
                        className={styles.levelInput}
                      />
                    </div>
                  ))
                : indicator.levels.map((level) => (
                    <div key={level.id} className={styles.level}>
                      <span className={styles.levelNumber}>
                        Nivel {level.level_number}
                      </span>
                      <span className={styles.levelTitle}>{level.title}</span>
                    </div>
                  ))}
            </div>
          </div>

          <div className={styles.section}>
            <h2 className={styles.sectionTitle}>Información</h2>
            <div className={styles.info}>
              <p>
                <strong>ID:</strong> {indicator.id}
              </p>
              <p>
                <strong>Creado:</strong>{" "}
                {new Date(indicator.created_at).toLocaleDateString()}
              </p>
              <p>
                <strong>Última actualización:</strong>{" "}
                {new Date(indicator.updated_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
