"use client";

import { useEffect, useState } from "react";
import { supabase } from "@/utils/supabase/client";
import { useRouter } from "next/navigation";
import styles from "./page.module.css";
import type { User } from "./types";
import Image from "next/image";
import SchoolOutlinedIcon from "@mui/icons-material/SchoolOutlined";
import AssessmentOutlinedIcon from "@mui/icons-material/AssessmentOutlined";
import PeopleOutlineIcon from "@mui/icons-material/PeopleOutline";
import GradingIcon from "@mui/icons-material/Grading";
import { AuthHandler } from "@/components/AuthHandler/AuthHandler";

export default function Home() {
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchSession = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUser(user as unknown as User);
    };

    fetchSession();

    // Suscribirse a cambios en la autenticación para actualizar el estado cuando se cierra sesión
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      if (session?.user) {
        setUser(session.user as unknown as User);
      } else {
        setUser(null);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const features = [
    {
      id: "courses",
      icon: <SchoolOutlinedIcon className={styles.featureIcon} />,
      title: "Gestión de Cursos",
      description:
        "Organiza tus cursos y asignaturas de forma intuitiva y eficiente.",
    },
    {
      id: "criteria",
      icon: <AssessmentOutlinedIcon className={styles.featureIcon} />,
      title: "Criterios e Indicadores",
      description:
        "Define criterios de evaluación detallados con indicadores personalizables.",
    },
    {
      id: "students",
      icon: <PeopleOutlineIcon className={styles.featureIcon} />,
      title: "Seguimiento de Alumnos",
      description:
        "Monitoriza el progreso individual de cada estudiante con informes detallados.",
    },
    {
      id: "qualitative",
      icon: <GradingIcon className={styles.featureIcon} />,
      title: "Evaluación Cualitativa",
      description:
        "Combina evaluaciones cuantitativas y cualitativas para un análisis completo.",
    },
  ];

  return (
    <div className={styles.container}>
      {/* Componente para manejar la autenticación */}
      <AuthHandler />
      {/* Hero Section */}
      <div className={styles.hero}>
        <div className={styles.heroContent}>
          <h1 className={styles.title}>
            <span className={styles.titleHighlight}>EvalTool</span>
            <span className={styles.titleSecondary}>2025</span>
          </h1>
          <h2 className={styles.subtitle}>Evaluación educativa simplificada</h2>
          <p className={styles.description}>
            Gestiona las evaluaciones de tus asignaturas de manera eficiente y
            organizada. Crea y administra criterios de evaluación, indicadores y
            seguimiento del progreso de tus alumnos en un solo lugar.
          </p>
          {user ? (
            <button
              className={styles.ctaButton}
              onClick={() => router.push("/courses")}
            >
              Ver mis cursos
            </button>
          ) : (
            <div className={styles.ctaContainer}>
              <button
                className={styles.ctaButton}
                onClick={() => router.push("/login")}
              >
                Iniciar sesión
              </button>
              <p className={styles.loginMessage}>
                Inicia sesión para comenzar a gestionar tus evaluaciones
              </p>
            </div>
          )}
        </div>
        <div className={styles.heroImageContainer}>
          <div className={styles.heroImage}>
            <Image
              src="/images/hero-illustration.svg"
              alt="Ilustración de evaluación educativa"
              width={500}
              height={400}
              priority
            />
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className={styles.featuresSection}>
        <h2 className={styles.sectionTitle}>Características principales</h2>
        <div className={styles.featuresGrid}>
          {features.map((feature) => (
            <div key={feature.id} className={styles.featureCard}>
              <div className={styles.featureIconContainer}>{feature.icon}</div>
              <h3 className={styles.featureTitle}>{feature.title}</h3>
              <p className={styles.featureDescription}>{feature.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* How It Works Section */}
      <div className={styles.howItWorksSection}>
        <h2 className={styles.sectionTitle}>Cómo funciona</h2>
        <div className={styles.stepsContainer}>
          <div className={styles.step}>
            <div className={styles.stepNumber}>1</div>
            <h3 className={styles.stepTitle}>Configura tus cursos</h3>
            <p className={styles.stepDescription}>
              Crea cursos y asignaturas, y define los criterios de evaluación.
            </p>
          </div>
          <div className={styles.step}>
            <div className={styles.stepNumber}>2</div>
            <h3 className={styles.stepTitle}>Añade alumnos y trabajos</h3>
            <p className={styles.stepDescription}>
              Registra a tus estudiantes y crea trabajos para evaluar.
            </p>
          </div>
          <div className={styles.step}>
            <div className={styles.stepNumber}>3</div>
            <h3 className={styles.stepTitle}>Evalúa y analiza</h3>
            <p className={styles.stepDescription}>
              Realiza evaluaciones y visualiza informes detallados del progreso.
            </p>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className={styles.ctaSection}>
        <div className={styles.ctaContent}>
          <h2 className={styles.ctaTitle}>¿Listo para empezar?</h2>
          <p className={styles.ctaDescription}>
            Comienza a utilizar EvalTool hoy mismo y transforma tu forma de
            evaluar.
          </p>
          {!user && (
            <button
              className={styles.ctaButtonLarge}
              onClick={() => router.push("/login")}
            >
              Iniciar sesión
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
