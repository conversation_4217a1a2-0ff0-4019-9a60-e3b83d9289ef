:root {
  --max-width: 1100px;
  --border-radius: 12px;
  --font-mono: ui-monospace, Menlo, Monaco, "Cascadia Mono", "Segoe UI Mono",
    "Roboto Mono", "Oxygen Mono", "Ubuntu Monospace", "Source Code Pro",
    "Fira Mono", "Droid Sans Mono", "Courier New", monospace;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  background: linear-gradient(
    to bottom,
    #f0f9ff 0%,
    #e0f2fe 25%,
    #bae6fd 50%,
    #7dd3fc 75%,
    #38bdf8 100%
  );
}

body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  margin-top: 64px;
  padding: 24px;
  flex: 1;
  width: 100%;
  max-width: var(--max-width);
  margin-left: auto;
  margin-right: auto;
}

a {
  color: inherit;
  text-decoration: none;
}
