import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: Request) {
  try {
    const { title, description, user_id } = await request.json();
    const slug = title.toLowerCase().replace(/\s+/g, "-");

    const { data: course, error } = await supabase
      .from("courses")
      .insert([
        {
          title,
          description,
          user_id,
          slug,
          created_at: new Date().toISOString(),
        },
      ])
      .select("id, title, description, user_id, slug");

    if (error) {
      console.error("Error al crear curso:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(course[0]);
  } catch (error) {
    console.error("Error en la API:", error);
    return NextResponse.json(
      { error: "Error interno del servidor" },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json(
        { error: "Se requiere el ID del usuario" },
        { status: 400 }
      );
    }

    console.log("Obteniendo cursos para usuario:", userId);
    const { data: courses, error } = await supabase
      .from("courses")
      .select("*")
      .eq("user_id", userId);

    if (error) {
      console.error("Error al obtener cursos:", error);
      return NextResponse.json(
        { error: error.message || "Error al obtener los cursos" },
        { status: 500 }
      );
    }

    if (!courses) {
      return NextResponse.json([]);
    }

    const sortedCourses = courses.sort((a, b) =>
      a.description.localeCompare(b.description)
    );

    return NextResponse.json(sortedCourses);
  } catch (error) {
    console.error("Error en la API:", error);
    return NextResponse.json(
      { error: "Error interno del servidor" },
      { status: 500 }
    );
  }
}
