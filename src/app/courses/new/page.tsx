"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON><PERSON>, But<PERSON> } from "@mui/material";
import { useDomain } from "@/components/Context/DomainContext";
import { supabase } from "@/utils/supabase/client";
import { ErrorModal } from "@/components/ErrorModal/ErrorModal";
import styles from "./new.module.css";

interface FormData {
  title: string;
  description: string;
}

export default function NewCoursePage() {
  const router = useRouter();
  const { domain } = useDomain();
  const [formData, setFormData] = useState<FormData>({
    title: "",
    description: "",
  });
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session?.user) {
        throw new Error("No hay usuario autenticado");
      }

      await domain.addCourseUseCase.execute({
        title: formData.title,
        description: formData.description,
        user_id: session.user.id,
      });

      router.push("/courses");
    } catch (error) {
      console.error("Error al crear el curso:", error);
      setError("Error al crear el curso");
    }
  };

  return (
    <div className={styles.container}>
      <h1>Nuevo Curso</h1>
      <form onSubmit={handleSubmit} className={styles.form}>
        <TextField
          autoFocus
          required
          name="title"
          label="Título del curso"
          value={formData.title}
          onChange={handleInputChange}
          fullWidth
          margin="normal"
        />
        <TextField
          required
          name="description"
          label="Descripción"
          value={formData.description}
          onChange={handleInputChange}
          fullWidth
          multiline
          rows={4}
          margin="normal"
        />
        <div className={styles.actions}>
          <Button onClick={() => router.back()} variant="outlined">
            Cancelar
          </Button>
          <Button type="submit" variant="contained" color="primary">
            Crear Curso
          </Button>
        </div>
      </form>

      <ErrorModal
        open={!!error}
        message={error || ""}
        onClose={() => setError(null)}
      />
    </div>
  );
}
