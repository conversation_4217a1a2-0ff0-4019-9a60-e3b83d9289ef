/* Container principal */
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
  padding: 2rem;
}

/* Hero Section */
.heroSection {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 3rem;
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.heroContent {
  flex: 1;
  max-width: 600px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--title-color);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.heroImageContainer {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.heroImage {
  max-width: 100%;
  height: auto;
}

/* Create Button */
.createButton {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--primary) 0%, #38bdf8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(29, 136, 175, 0.2);
}

.createButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(29, 136, 175, 0.3);
}

/* Info Section */
.infoSection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.infoCard {
  background-color: var(--bg-primary);
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.infoCard:hover {
  transform: translateY(-5px);
}

.infoIcon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(29, 136, 175, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.infoCard h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 0.75rem;
}

.infoCard p {
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: 0.95rem;
}

/* Courses Section */
.coursesSection {
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--title-color);
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--border-color);
}

.coursesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  width: 100%;
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  text-align: center;
}

.emptyStateIcon {
  margin-bottom: 1.5rem;
  opacity: 0.6;
}

.emptyMessage {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 0.5rem;
}

.emptyDescription {
  color: var(--text-secondary);
  max-width: 400px;
  line-height: 1.5;
}

/* Loading State */
.loading {
  text-align: center;
  padding: 4rem 2rem;
  font-size: 1.2rem;
  color: var(--title-color);
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-primary);
  border-radius: 12px;
}

/* Error State */
.error {
  text-align: center;
  padding: 2rem;
  color: #e74c3c;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-primary);
  border-radius: 12px;
}

.error button {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.error button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .heroSection {
    flex-direction: column;
    text-align: center;
    padding: 2rem 1.5rem;
  }

  .heroContent {
    max-width: 100%;
    margin-bottom: 2rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .title {
    font-size: 2rem;
  }

  .description {
    font-size: 1rem;
  }

  .coursesGrid {
    grid-template-columns: 1fr;
  }

  .infoSection {
    grid-template-columns: 1fr;
  }
}

.courseCard {
  background-color: var(--bg-primary);
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.courseCard:hover {
  transform: translateY(-2px);
}

.courseHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.courseTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
}

.courseDescription {
  color: #64748b;
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
}

.courseActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.actionButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.actionButton:hover {
  background-color: #2563eb;
}

.deleteButton {
  color: #64748b;
  padding: 0.25rem;
  transition: color 0.2s ease;
}

.deleteButton:hover {
  color: #ef4444;
}

@media (max-width: 768px) {
  .coursesGrid {
    grid-template-columns: 1fr;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
