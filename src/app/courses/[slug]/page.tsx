"use client";

import { useEffect, useState, use } from "react";
import { useRouter } from "next/navigation";
import styles from "./course-detail.module.css";
import { useDomain } from "@/components/Context";
import { Breadcrumbs } from "@/components/Breadcrumbs/Breadcrumbs";

interface Subject {
  id: string;
  name: string;
  slug: string;
}

interface Course {
  id: string;
  title: string;
  description: string;
  user_id: string;
  slug: string;
  subjects: Subject[];
}

// Props para la página de curso
interface CoursePageProps {
  params: Promise<{ slug: string }>;
}

export default function CoursePage({ params }: Readonly<CoursePageProps>) {
  const router = useRouter();
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { domain } = useDomain();
  const resolvedParams = use(params);

  useEffect(() => {
    const fetchCourse = async () => {
      try {
        const courseData = await domain.getCourseBySlugUseCase.execute({
          slug: resolvedParams.slug,
        });

        if (!courseData) {
          setError("Curso no encontrado");
          return;
        }

        setCourse(courseData);
      } catch (err) {
        console.error("Error fetching course:", err);
        setError("Error al cargar los detalles del curso");
      } finally {
        setLoading(false);
      }
    };

    fetchCourse();
  }, [resolvedParams.slug]);

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.courseDetail}>
          <p>Cargando...</p>
        </div>
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className={styles.container}>
        <div className={styles.courseDetail}>
          <p className={styles.error}>{error ?? "Curso no encontrado"}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Breadcrumbs
        items={[{ label: "Cursos", href: "/courses" }, { label: course.title }]}
      />
      <div className={styles.courseDetail}>
        <div className={styles.header}>
          <h1 className={styles.title}>{course.title}</h1>
        </div>

        <p className={styles.description}>{course.description}</p>

        <div className={styles.subjectsSection}>
          <div className={styles.sectionHeader}>
            <h2>Asignaturas</h2>
          </div>

          <div className={styles.subjectsList}>
            {course.subjects.length === 0 ? (
              <p className={styles.emptyMessage}>
                No hay asignaturas disponibles
              </p>
            ) : (
              course.subjects.map((subject) => (
                <div key={subject.id} className={styles.subjectItem}>
                  <div className={styles.subjectInfo}>
                    <h3 className={styles.subjectTitle}>{subject.name}</h3>
                  </div>
                  <button
                    onClick={() => router.push(`/subjects/${subject.slug}`)}
                    className={styles.accessButton}
                  >
                    Ver detalles
                  </button>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
