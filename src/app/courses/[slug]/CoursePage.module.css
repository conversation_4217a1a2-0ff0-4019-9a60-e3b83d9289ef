.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.heading {
  margin-bottom: 20px;
}

.gridContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.gridItem {
  width: 100%;
}

.listContainer {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 2px solid #000000;
  padding: 24px;
  background-color: rgba(0, 255, 255, 0.076);
  font-size: 18px;
  margin-bottom: 8px;
  height: 100%;
}

.listContainer li {
  list-style: none;
}

.ctaContainer {
  display: flex;
  gap: 16px;
  margin-top: 20px;
}
