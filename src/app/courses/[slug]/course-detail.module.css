.container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.backButton {
  background: none;
  border: none;
  color: #64748b;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: color 0.2s ease;
  margin-bottom: 1rem;
}

.backButton:hover {
  color: var(--title-color);
}

.courseDetail {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header {
  margin-bottom: 2rem;
}

.title {
  font-size: 2rem;
  color: var(--title-color);
  margin: 0;
}

.description {
  color: var(--title-color);
  font-size: 1.125rem;
  line-height: 1.6;
  margin: 1rem 0 2rem 0;
}

.subjectsSection {
  margin-top: 2rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.sectionHeader h2 {
  font-size: 1.5rem;
  color: var(--title-color);
  margin: 0;
}

.subjectsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.subjectItem {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  background-color: var(--bg-primary);
  padding: 1.5rem;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.subjectItem:hover {
  background-color: #f1f5f9;
}

.subjectInfo {
  flex: 1;
}

.subjectTitle {
  font-size: 1.25rem;
  color: var(--title-color);
  margin: 0 0 0.5rem 0;
}

.subjectDescription {
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.accessButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.accessButton:hover {
  background-color: #2563eb;
}

.emptyMessage {
  color: #64748b;
  text-align: center;
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 8px;
}

.error {
  color: #ef4444;
  text-align: center;
  padding: 2rem;
  background-color: #fef2f2;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .courseDetail {
    padding: 1.5rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .subjectItem {
    flex-direction: column;
    gap: 1rem;
  }

  .accessButton {
    width: 100%;
  }
}
