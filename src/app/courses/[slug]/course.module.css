.container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.backButton {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 0;
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  transition: color 0.2s ease;
}

.backButton:hover {
  color: #2563eb;
}

.courseDetail {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--title-color);
  margin: 0;
  margin-right: 1rem;
}

.description {
  color: #64748b;
  font-size: 1rem;
  margin: 0 0 2rem 0;
}

.subjectsSection {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--outline-color);
}

.subjectsList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.subjectChip {
  width: 100%;
  justify-content: flex-start;
  background-color: #f1f5f9;
  color: var(--title-color);
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
  border-radius: 0 !important;
  height: 48px;
  font-size: 1rem;
}

.subjectChip :global(.MuiChip-root) {
  border-radius: 0 !important;
}

.subjectChip:hover {
  background-color: #e2e8f0;
  cursor: pointer;
}

.subjectChip.active {
  background-color: #3b82f6;
  color: white;
  border-color: #2563eb;
}

.emptyMessage {
  color: #64748b;
  text-align: center;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .title {
    font-size: 1.5rem;
  }
}
