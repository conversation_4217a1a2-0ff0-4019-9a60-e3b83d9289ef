"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/utils/supabase/client";
import { Course } from "@/domain/courses/entities/Course";
import { CourseCard } from "@/components/CourseCard/CourseCard";
import { useDomain } from "@/components/Context/DomainContext";
import { ErrorModal } from "@/components/ErrorModal/ErrorModal";
import {
  SchoolIcon,
  ChartIcon,
  PeopleIcon,
  EmptyFolderIcon,
} from "@/components/icons";
import { HeroSection, HeroVariant } from "@/components/HeroSection";
import styles from "./courses.module.css";

export default function CoursesPage() {
  const router = useRouter();
  const { domain } = useDomain();
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log("Iniciando CoursesPage useEffect");
    const fetchCourses = async () => {
      try {
        console.log("Obteniendo sesión actual...");
        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession();

        if (sessionError) {
          console.error("Error al obtener la sesión:", sessionError);
          throw sessionError;
        }

        if (!session?.user?.id) {
          console.error("No hay usuario autenticado");
          setError("No hay usuario autenticado");
          setIsLoading(false);
          return;
        }

        console.log("Usuario autenticado:", session.user.id);
        console.log("Ejecutando caso de uso...");
        const coursesData = await domain.getCoursesListUseCase.execute(
          session.user.id
        );
        console.log("Cursos obtenidos:", coursesData);

        setCourses(coursesData);
        setError(null);
      } catch (error) {
        console.error("Error en fetchCourses:", error);
        setError(
          error instanceof Error ? error.message : "Error al cargar los cursos"
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourses();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      console.log("Cambio en el estado de autenticación:", _event);
      if (session?.user?.id) {
        fetchCourses();
      } else {
        setCourses([]);
        setError(null);
        setIsLoading(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [domain]);

  const handleDeleteCourse = (courseId: string) => {
    setCourses((prevCourses) =>
      prevCourses.filter((course) => course.id !== courseId)
    );
  };

  if (isLoading) {
    return <div className={styles.loading}>Cargando cursos...</div>;
  }

  return (
    <div className={styles.container}>
      <HeroSection
        title="Mis Cursos"
        description="Los cursos son la unidad principal de organización en EvalTool. Cada curso puede contener múltiples asignaturas, estudiantes y evaluaciones. Utiliza esta sección para gestionar tus cursos académicos."
        ctaText="Crear Curso"
        ctaUrl="/courses/new"
        variant={HeroVariant.COURSES}
      />

      <div className={styles.infoSection}>
        <div className={styles.infoCard}>
          <div className={styles.infoIcon}>
            <SchoolIcon style={{ color: "var(--primary)" }} />
          </div>
          <h3>Organización</h3>
          <p>
            Agrupa tus asignaturas por cursos académicos para mantener todo
            organizado.
          </p>
        </div>
        <div className={styles.infoCard}>
          <div className={styles.infoIcon}>
            <PeopleIcon style={{ color: "var(--primary)" }} />
          </div>
          <h3>Estudiantes</h3>
          <p>
            Asigna estudiantes a tus cursos y realiza un seguimiento de su
            progreso académico.
          </p>
        </div>
        <div className={styles.infoCard}>
          <div className={styles.infoIcon}>
            <ChartIcon style={{ color: "var(--primary)" }} />
          </div>
          <h3>Evaluación</h3>
          <p>
            Configura criterios de evaluación y realiza un seguimiento detallado
            del rendimiento.
          </p>
        </div>
        <div className={styles.infoCard}>
          <div className={styles.infoIcon}>
            <ChartIcon style={{ color: "var(--primary)" }} />
          </div>
          <h3>Reporte</h3>
          <p>
            Centraliza toda la información relativa al rendimiento académico y
            genera informes personalizados.
          </p>
        </div>
      </div>

      <div className={styles.coursesSection}>
        <h2 className={styles.sectionTitle}>Cursos disponibles</h2>
        {courses.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyStateIcon}>
              <EmptyFolderIcon style={{ color: "var(--text-secondary)" }} />
            </div>
            <p className={styles.emptyMessage}>No hay cursos disponibles</p>
            <p className={styles.emptyDescription}>
              Crea tu primer curso para comenzar a gestionar tus evaluaciones
            </p>
          </div>
        ) : (
          <div className={styles.coursesGrid}>
            {courses.map((course) => (
              <CourseCard
                key={course.id}
                course={course}
                onDelete={handleDeleteCourse}
              />
            ))}
          </div>
        )}
      </div>

      <ErrorModal
        open={!!error}
        message={error ?? ""}
        onClose={() => {
          setError(null);
          if (error === "No hay usuario autenticado") {
            router.push("/login");
          }
        }}
      />
    </div>
  );
}
