"use client";

import { Container, Typography, Paper } from "@mui/material";
import { styled } from "@mui/material/styles";
import { AuthButton } from "@/components/AuthButton/AuthButton";
import { Suspense, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useDomain } from "@/components/Context";

const StyledPaper = styled(Paper)(({ theme }) => ({
  marginTop: theme.spacing(8),
  padding: theme.spacing(4),
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
}));

function LoginContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const rawRedirectTo = searchParams.get("redirectTo");
  const redirectTo = rawRedirectTo ? decodeURIComponent(rawRedirectTo) : "/";
  console.log("LoginContent - redirectTo:", redirectTo);
  const { domain } = useDomain();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { isAuthenticated } =
          await domain.isAuthenticatedUseCase.execute();
        if (isAuthenticated) {
          console.log(
            "LoginPage - Usuario ya autenticado, redirigiendo a:",
            redirectTo
          );
          router.push(redirectTo);
        }
      } catch (error) {
        console.error("LoginPage - Error al verificar autenticación:", error);
      }
    };

    checkAuth();
  }, [redirectTo, router, domain]);

  return (
    <Container component="main" maxWidth="xs">
      <StyledPaper elevation={3}>
        <Typography component="h1" variant="h5" gutterBottom>
          Iniciar sesión
        </Typography>
        <Typography variant="body1" sx={{ mb: 4, textAlign: "center" }}>
          Accede a tu cuenta usando Google
        </Typography>
        <Suspense fallback={<div>Cargando...</div>}>
          <AuthButton isLoggedIn={false} redirectTo={redirectTo} />
        </Suspense>
      </StyledPaper>
    </Container>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={<div>Cargando...</div>}>
      <LoginContent />
    </Suspense>
  );
}
