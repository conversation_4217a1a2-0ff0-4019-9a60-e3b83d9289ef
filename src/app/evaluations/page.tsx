"use client";

import { useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { useDomain } from "@/components/Context";
import { HeroSection, HeroVariant } from "@/components/HeroSection";
import { HomeworkForEvaluation } from "@/domain/evaluations/types";
import styles from "./styles.module.css";
import { SearchIcon } from "@/components/icons";

export default function EvaluationsPage() {
  const router = useRouter();
  const { domain } = useDomain();
  const [homeworks, setHomeworks] = useState<HomeworkForEvaluation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSubjectId, setSelectedSubjectId] = useState<string | null>(
    null
  );

  useEffect(() => {
    const fetchHomeworks = async () => {
      try {
        const homeworksList =
          await domain.getHomeworksBasicInfoUseCase.execute();
        setHomeworks(homeworksList);
      } catch (err) {
        console.error("Error fetching homeworks:", err);
        setError("Error al cargar la lista de trabajos");
      } finally {
        setLoading(false);
      }
    };

    fetchHomeworks();
  }, [domain]);

  // Extraer asignaturas únicas para el filtro
  const subjects = useMemo(() => {
    const subjectsMap = new Map();
    homeworks.forEach((homework) => {
      if (homework.subject) {
        subjectsMap.set(homework.subject.id, homework.subject);
      }
    });
    return Array.from(subjectsMap.values());
  }, [homeworks]);

  // Filtrar trabajos por término de búsqueda y asignatura seleccionada
  const filteredHomeworks = useMemo(() => {
    return homeworks.filter((homework) => {
      const matchesSearch =
        homework.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (homework.subject?.name ?? "")
          .toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        homework.description.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesSubject =
        !selectedSubjectId || homework.subject?.id === selectedSubjectId;

      return matchesSearch && matchesSubject;
    });
  }, [homeworks, searchTerm, selectedSubjectId]);

  if (loading) {
    return <div className={styles.loading}>Cargando trabajos...</div>;
  }

  if (error) {
    return <div className={styles.error}>{error}</div>;
  }

  return (
    <div className={styles.container}>
      <HeroSection
        title="Evaluaciones"
        description="Evalúa el desempeño de tus estudiantes en los diferentes trabajos asignados. Selecciona un trabajo para ver la lista de estudiantes y evaluar sus indicadores."
        ctaText="Volver a Trabajos"
        ctaUrl="/homeworks"
        variant={HeroVariant.EVALUATIONS}
      />

      <div className={styles.filtersSection}>
        <div className={styles.searchContainer}>
          <input
            type="text"
            placeholder="Buscar trabajo..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={styles.searchInput}
          />
          <SearchIcon
            className={styles.searchIcon}
            style={{ color: "var(--text-secondary)" }}
          />
        </div>

        <div className={styles.subjectFilter}>
          <select
            value={selectedSubjectId ?? ""}
            onChange={(e) => setSelectedSubjectId(e.target.value || null)}
            className={styles.subjectSelect}
          >
            <option value="">Todas las asignaturas</option>
            {subjects.map((subject) => (
              <option key={subject.id} value={subject.id}>
                {subject.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className={styles.homeworksGrid}>
        {filteredHomeworks.length === 0 ? (
          <div className={styles.noResults}>
            No se encontraron trabajos. Intenta con otros filtros o crea nuevos
            trabajos.
          </div>
        ) : (
          filteredHomeworks.map((homework) => (
            <div key={homework.id} className={styles.homeworkCard}>
              <div className={styles.homeworkHeader}>
                <h3 className={styles.homeworkTitle}>{homework.title}</h3>
                <span className={styles.homeworkSubject}>
                  {homework.subject?.name}
                </span>
              </div>
              <p className={styles.homeworkDescription}>
                {homework.description.length > 100
                  ? `${homework.description.substring(0, 100)}...`
                  : homework.description}
              </p>
              <div className={styles.homeworkStats}>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>
                    Estudiantes evaluados:
                  </span>
                  <span className={styles.statValue}>
                    {homework.evaluatedStudentsCount ?? 0} /{" "}
                    {homework.totalStudentsCount ?? 0}
                  </span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>Criterios:</span>
                  <span className={styles.statValue}>
                    {homework.criterias?.length ?? 0}
                  </span>
                </div>
              </div>
              <div className={styles.homeworkFooter}>
                <button
                  onClick={() => router.push(`/evaluations/${homework.slug}`)}
                  className="btn-primary"
                >
                  Evaluar
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
