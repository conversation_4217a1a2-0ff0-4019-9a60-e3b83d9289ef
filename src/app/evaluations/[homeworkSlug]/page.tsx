"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useDomain } from "@/components/Context";
import { StudentForEvaluation } from "@/domain/evaluations/types";
import styles from "../styles.module.css";
import { SearchIcon } from "@/components/icons";

// Props para la página de estudiantes
interface StudentsPageProps {
  params: Promise<{ homeworkSlug: string }>;
}

export default function StudentsPage({ params }: Readonly<StudentsPageProps>) {
  const unwrappedParams = React.use(params);
  const { homeworkSlug } = unwrappedParams;
  const router = useRouter();
  const { domain } = useDomain();
  const [students, setStudents] = useState<StudentForEvaluation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [homeworkTitle, setHomeworkTitle] = useState("");

  useEffect(() => {
    const fetchStudents = async () => {
      try {
        const studentsList = await domain.getStudentsByHomeworkUseCase.execute({
          homeworkSlug,
        });
        setStudents(studentsList);

        // Obtener el título del trabajo usando la versión optimizada
        const homeworks = await domain.getHomeworksBasicInfoUseCase.execute();
        const homework = homeworks.find(
          (h: { slug: string }) => h.slug === homeworkSlug
        );
        if (homework) {
          setHomeworkTitle(homework.title);
        }
      } catch (err) {
        console.error("Error fetching students:", err);
        setError("Error al cargar la lista de estudiantes");
      } finally {
        setLoading(false);
      }
    };

    fetchStudents();
  }, [domain, homeworkSlug]);

  // Filtrar estudiantes por término de búsqueda
  const filteredStudents = students.filter((student) => {
    const fullName = `${student.first_name} ${student.last_name1} ${
      student.last_name2 ?? ""
    }`.toLowerCase();
    return fullName.includes(searchTerm.toLowerCase());
  });

  if (loading) {
    return <div className={styles.loading}>Cargando estudiantes...</div>;
  }

  if (error) {
    return <div className={styles.error}>{error}</div>;
  }

  return (
    <div className={styles.container}>
      <div className={styles.evaluationHeader}>
        <h1 className={styles.evaluationTitle}>Evaluación: {homeworkTitle}</h1>
        <p className={styles.evaluationSubtitle}>
          Selecciona un estudiante para evaluar
        </p>
      </div>

      <div className={styles.filtersSection}>
        <div className={styles.searchContainer}>
          <input
            type="text"
            placeholder="Buscar estudiante..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={styles.searchInput}
          />
          <SearchIcon
            className={styles.searchIcon}
            style={{ color: "var(--text-secondary)" }}
          />
        </div>

        <button
          onClick={() => router.push("/evaluations")}
          className={styles.backButton}
        >
          Volver a Evaluaciones
        </button>
      </div>

      <div className={styles.studentsList}>
        {filteredStudents.length === 0 ? (
          <div className={styles.noResults}>
            No se encontraron estudiantes. Intenta con otros filtros o añade
            estudiantes al curso.
          </div>
        ) : (
          filteredStudents.map((student) => (
            <div key={student.id} className={styles.studentCard}>
              <h3 className={styles.studentName}>
                {student.first_name} {student.last_name1}{" "}
                {student.last_name2 ?? ""}
              </h3>
              <div className={styles.studentInfo}>
                {student.is_pi ? "Necesidades especiales" : ""}
              </div>
              <div
                className={`${styles.evaluatedBadge} ${
                  student.isEvaluated ? styles.yes : styles.no
                }`}
              >
                {student.isEvaluated ? "Evaluado" : "No evaluado"}
              </div>
              <div className={styles.studentFooter}>
                <button
                  onClick={() =>
                    router.push(`/evaluations/${homeworkSlug}/${student.slug}`)
                  }
                  className="btn-primary"
                >
                  {student.isEvaluated ? "Ver evaluación" : "Evaluar"}
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
