.container {
  width: 100%;
  max-width: var(--max-width);
  margin: 0 auto;
}

.loading,
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: 1.2rem;
  color: var(--text-secondary);
}

.error {
  color: #e53e3e;
}

.filtersSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.searchContainer {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.9rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
}

.subjectFilter {
  min-width: 200px;
}

.subjectSelect {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.9rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
}

.homeworksGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.homeworkCard {
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
}

.homeworkCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.homeworkHeader {
  margin-bottom: 1rem;
}

.homeworkTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 0.5rem;
}

.homeworkSubject {
  display: inline-block;
  font-size: 0.85rem;
  color: var(--text-secondary);
  background-color: var(--bg-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
}

.homeworkDescription {
  font-size: 0.95rem;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.homeworkStats {
  margin-bottom: 1.5rem;
}

.statItem {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.statValue {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-primary);
}

.homeworkFooter {
  display: flex;
  justify-content: flex-end;
}

.noResults {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Estilos para la página de estudiantes */
.studentsList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.studentCard {
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
}

.studentCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.studentName {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 0.5rem;
}

.studentInfo {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.evaluatedBadge {
  display: inline-block;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.evaluatedBadge.yes {
  background-color: #c6f6d5;
  color: #22543d;
}

.evaluatedBadge.no {
  background-color: #fed7d7;
  color: #822727;
}

.studentFooter {
  margin-top: auto;
  display: flex;
  justify-content: flex-end;
}

/* Estilos para la página de evaluación */
.evaluationContainer {
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  margin-bottom: 2rem;
}

.evaluationHeader {
  margin-bottom: 2rem;
}

.evaluationTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 0.5rem;
}

.evaluationSubtitle {
  font-size: 1rem;
  color: var(--text-secondary);
}

.criteriaSection {
  margin-bottom: 2rem;
}

.criteriaTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.indicatorsList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.indicatorItem {
  background-color: var(--bg-secondary);
  border-radius: 8px;
  padding: 1.5rem;
}

.indicatorTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 1rem;
}

.levelsList {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.levelButton {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.levelButton:hover {
  background-color: var(--bg-hover);
}

.levelButton.selected {
  background-color: #0284c7;
  color: white;
  border-color: #0284c7;
}

.levelDescription {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
}

.evaluationActions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.backButton {
  padding: 0.75rem 1.25rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.backButton:hover {
  background-color: var(--bg-hover);
}

.saveButton {
  padding: 0.75rem 1.25rem;
  background-color: #0284c7;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.saveButton:hover {
  background-color: #0369a1;
}

.saveButton:disabled {
  background-color: #cbd5e0;
  cursor: not-allowed;
}

.successMessage {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #c6f6d5;
  border-radius: 8px;
  text-align: center;
}

.successMessage p {
  font-size: 1.1rem;
  color: #22543d;
  font-weight: 600;
}

.debugButton {
  padding: 0.75rem 1.25rem;
  background-color: #6b7280;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.debugButton:hover {
  background-color: #4b5563;
}

.noLevels {
  padding: 0.75rem;
  background-color: #fee2e2;
  color: #991b1b;
  border-radius: 6px;
  font-size: 0.9rem;
  text-align: center;
}
