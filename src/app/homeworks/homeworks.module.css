/* Container principal */
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
  padding: 2rem;
}

.homeworksDetail {
  margin-bottom: 2rem;
}

/* Filters Section */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem 1.5rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.headerActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 1rem;
}

.searchContainer {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.95rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(29, 136, 175, 0.1);
}

.searchInput::placeholder {
  color: var(--text-secondary);
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--title-color);
  margin: 0;
}

.groupToggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: var(--bg-primary);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--outline-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.groupToggle label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--title-color);
  cursor: pointer;
  user-select: none;
}

.groupToggle input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.groupToggle .toggle {
  position: relative;
  width: 44px;
  height: 24px;
  background-color: #e2e8f0;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.groupToggle .toggle:before {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  background-color: var(--bg-primary);
  transition: transform 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.groupToggle input[type="checkbox"]:checked + .toggle {
  background-color: #3b82f6;
}

.groupToggle input[type="checkbox"]:checked + .toggle:before {
  transform: translateX(20px);
}

.subjectGroup {
  margin-bottom: 2rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  grid-column: 1 / -1; /* Hace que el grupo ocupe todo el ancho */
}

.subjectGroup:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.subjectGroupTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
  padding: 1.25rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.subjectGroup .homeworksList {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  padding: 1rem;
}

@media (min-width: 768px) {
  .subjectGroup .homeworksList {
    grid-template-columns: repeat(2, 1fr);
  }
}

.subjectGroup .homeworkItem {
  margin: 0;
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  border-radius: 8px;
}

.subjectGroup .homeworkItem:hover {
  background-color: var(--bg-primary);
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.homeworksList {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  width: 100%;
}

@media (min-width: 768px) {
  .homeworksList {
    grid-template-columns: repeat(2, 1fr);
  }
}

.homeworkItem {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  border-radius: 0 0 12px 12px;
  border: 1px solid var(--border-color);
  overflow: visible;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  margin-bottom: 1rem;
}

.homeworkItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.homeworkHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  position: relative;
}

.homeworkContent {
  padding: 1.25rem;
  flex: 1;
}

.homeworkFooter {
  padding: 1.25rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
}

.homeworkInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.homeworkTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0;
  padding-right: 2rem;
}

.homeworkSubject {
  font-size: 0.875rem;
  margin: 0.5rem 0 0 0;
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: rgba(29, 136, 175, 0.1);
  color: var(--primary);
  border-radius: 4px;
  font-weight: 600;
  max-width: fit-content;
}

.btn-close {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  color: var(--text-secondary);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.btn-close:hover {
  color: #ef4444;
}

.homeworkDueDate {
  color: #64748b;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dueDateLabel {
  font-weight: 500;
}

.dueDateValue {
  color: var(--title-color);
}

.pastDue {
  color: #ef4444;
}

.accessButton {
  background: linear-gradient(135deg, var(--primary) 0%, #38bdf8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(29, 136, 175, 0.2);
}

.accessButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(29, 136, 175, 0.3);
}

.emptyMessage {
  color: #64748b;
  text-align: center;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 8px;
}

.addButton {
  width: 100%;
  background-color: #f1f5f9;
  color: var(--title-color);
  border: 1px dashed #cbd5e1;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  margin-top: 1rem;
}

.addButton:hover {
  background-color: #e2e8f0;
  border-color: #94a3b8;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .homeworkItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .headerActions {
    flex-direction: column;
    align-items: flex-start;
  }

  .searchInput {
    width: 100%;
  }
}
