"use client";

import { useEffect, useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import styles from "./homeworks.module.css";
import { useDomain } from "@/components/Context";
import { HeroSection, HeroVariant } from "@/components/HeroSection";
import { SearchIcon, CloseIcon } from "@/components/icons";
import { ConfirmModal } from "@/components/ConfirmModal/ConfirmModal";

interface Homework {
  id: string;
  title: string;
  description: string;
  subject_id: string;
  created_at: string;
  updated_at: string;
  slug: string;
  subject: {
    id: string;
    name: string;
    slug: string;
  };
}

interface GroupedHomework {
  subjectName: string;
  subjectSlug: string;
  homeworks: Homework[];
}

export default function HomeworksPage() {
  const router = useRouter();
  const [homeworks, setHomeworks] = useState<Homework[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [groupBySubject, setGroupBySubject] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [homeworkToDelete, setHomeworkToDelete] = useState<string | null>(null);
  const { domain } = useDomain();

  useEffect(() => {
    const fetchHomeworks = async () => {
      try {
        const homeworksList = await domain.getHomeworksListUseCase.execute();
        setHomeworks(homeworksList);
      } catch (err) {
        console.error("Error fetching homeworks:", err);
        setError("Error al cargar la lista de trabajos");
      } finally {
        setLoading(false);
      }
    };

    fetchHomeworks();
  }, [domain]);

  const filteredHomeworks = useMemo(() => {
    return homeworks.filter(
      (homework) =>
        homework.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        homework.subject.name
          .toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        homework.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [homeworks, searchTerm]);

  const groupedHomeworks = useMemo<Homework[] | GroupedHomework[]>(() => {
    if (!groupBySubject) return filteredHomeworks;

    const groups = filteredHomeworks.reduce((acc, homework) => {
      const subjectName = homework.subject.name;
      if (!acc[subjectName]) {
        acc[subjectName] = {
          subjectName,
          subjectSlug: homework.subject.slug,
          homeworks: [],
        };
      }
      acc[subjectName].homeworks.push(homework);
      return acc;
    }, {} as Record<string, GroupedHomework>);

    return Object.values(groups);
  }, [filteredHomeworks, groupBySubject]);

  const handleAddHomework = () => {
    router.push("/homeworks/new");
  };

  const handleViewHomework = (homework: Homework) => {
    router.push(
      `/subjects/${homework.subject.slug}/homeworks/${homework.slug}`
    );
  };

  const handleDeleteHomework = (id: string) => {
    setHomeworkToDelete(id);
    setShowDeleteModal(true);
  };

  const confirmDeleteHomework = async () => {
    if (!homeworkToDelete) return;

    try {
      await domain.homeworks.deleteHomework(homeworkToDelete);
      setHomeworks(homeworks.filter((h) => h.id !== homeworkToDelete));
      setShowDeleteModal(false);
      setHomeworkToDelete(null);
    } catch (error) {
      console.error("Error deleting homework:", error);
      setError("Error al eliminar el trabajo");
    }
  };

  const getEmptyMessage = () => {
    return searchTerm
      ? "No se encontraron resultados"
      : "No hay trabajos disponibles";
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.homeworksDetail}>
          <p>Cargando...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.homeworksDetail}>
          <p className={styles.error}>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <HeroSection
        title="Trabajos"
        description="Gestiona los trabajos y tareas asignados a tus estudiantes. Cada trabajo puede contener múltiples criterios e indicadores de evaluación para medir el desempeño de manera objetiva."
        ctaText="Añadir trabajo"
        ctaUrl="/homeworks/new"
        variant={HeroVariant.HOMEWORKS}
      />
      <div className={styles.homeworksDetail}>
        <div className={styles.header}>
          <div className={styles.headerActions}>
            <div className={styles.searchContainer}>
              <input
                type="text"
                placeholder="Buscar trabajo o asignatura..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={styles.searchInput}
              />
              <SearchIcon
                className={styles.searchIcon}
                style={{ color: "var(--text-secondary)" }}
              />
            </div>
            <div className={styles.groupToggle}>
              <label htmlFor="groupToggle">Agrupar por asignatura</label>
              <input
                id="groupToggle"
                type="checkbox"
                checked={groupBySubject}
                onChange={(e) => setGroupBySubject(e.target.checked)}
              />
              <div
                className={styles.toggle}
                role="switch"
                aria-checked={groupBySubject}
                tabIndex={0}
                onClick={() => setGroupBySubject(!groupBySubject)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    setGroupBySubject(!groupBySubject);
                  }
                }}
              />
            </div>
          </div>
        </div>

        {groupedHomeworks.length === 0 ? (
          <p className={styles.emptyMessage}>{getEmptyMessage()}</p>
        ) : groupBySubject ? (
          <div className={styles.homeworksList}>
            {(groupedHomeworks as GroupedHomework[]).map((group) => (
              <div key={group.subjectName} className={styles.subjectGroup}>
                <h3 className={styles.subjectGroupTitle}>
                  {group.subjectName}
                </h3>
                <div className={styles.homeworksList}>
                  {group.homeworks.map((homework) => (
                    <div key={homework.id} className={styles.homeworkItem}>
                      <div className={styles.homeworkHeader}>
                        <h3 className={styles.homeworkTitle}>
                          {homework.title}
                        </h3>
                        <button
                          onClick={() => handleDeleteHomework(homework.id)}
                          className="btn-close"
                          aria-label={`Eliminar trabajo ${homework.title}`}
                        >
                          <CloseIcon />
                        </button>
                      </div>
                      <div className={styles.homeworkContent}>
                        <div className={styles.homeworkInfo}>
                          <p className={styles.homeworkSubject}>
                            {group.subjectName}
                          </p>
                        </div>
                      </div>
                      <div className={styles.homeworkFooter}>
                        <button
                          onClick={() => handleViewHomework(homework)}
                          className={styles.accessButton}
                        >
                          Ver detalle
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.homeworksList}>
            {(groupedHomeworks as Homework[]).map((homework) => (
              <div key={homework.id} className={styles.homeworkItem}>
                <div className={styles.homeworkHeader}>
                  <h3 className={styles.homeworkTitle}>{homework.title}</h3>
                  <button
                    onClick={() => handleDeleteHomework(homework.id)}
                    className="btn-close"
                    aria-label={`Eliminar trabajo ${homework.title}`}
                  >
                    <CloseIcon />
                  </button>
                </div>
                <div className={styles.homeworkContent}>
                  <div className={styles.homeworkInfo}>
                    <p className={styles.homeworkSubject}>
                      {homework.subject.name}
                    </p>
                  </div>
                </div>
                <div className={styles.homeworkFooter}>
                  <button
                    onClick={() => handleViewHomework(homework)}
                    className={styles.accessButton}
                  >
                    Ver detalle
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        <button onClick={handleAddHomework} className={styles.addButton}>
          + Añadir trabajo
        </button>
      </div>

      {showDeleteModal && (
        <ConfirmModal
          open={showDeleteModal}
          title="Eliminar trabajo"
          message="¿Estás seguro de que deseas eliminar este trabajo? Esta acción no se puede deshacer."
          onConfirm={confirmDeleteHomework}
          onCancel={() => {
            setShowDeleteModal(false);
            setHomeworkToDelete(null);
          }}
        />
      )}
    </div>
  );
}
