.container {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.formCard {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header {
  margin-bottom: 2rem;
}

.title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--title-color);
  margin-top: 0.5rem;
  margin-bottom: 1rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--title-color);
}

.input,
.select {
  padding: 0.75rem;
  border: 1px solid var(--outline-color);
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.input:focus,
.select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.formActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.submitButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submitButton:hover {
  background-color: #2563eb;
}

.backButton {
  background: none;
  border: none;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0;
  transition: color 0.2s ease;
}

.backButton:hover {
  color: var(--title-color);
}

.error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .formCard {
    padding: 1.5rem;
  }

  .title {
    font-size: 1.5rem;
  }
}
