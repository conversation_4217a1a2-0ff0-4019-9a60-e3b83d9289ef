"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useDomain } from "@/components/Context";
import styles from "./new-homework.module.css";

interface Subject {
  id: string;
  name: string;
  slug: string;
  course: {
    id: string;
    title: string;
  };
}

export default function NewHomeworkPage() {
  const router = useRouter();
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    subject_id: "",
  });
  const { domain } = useDomain();

  useEffect(() => {
    const fetchSubjects = async () => {
      try {
        const subjectsList = await domain.getSubjectsListUseCase.execute();
        setSubjects(subjectsList);
        if (subjectsList.length > 0) {
          setFormData((prev) => ({
            ...prev,
            subject_id: subjectsList[0].id,
          }));
        }
      } catch (err) {
        console.error("Error fetching subjects:", err);
        setError("Error al cargar la lista de asignaturas");
      } finally {
        setLoading(false);
      }
    };

    fetchSubjects();
  }, [domain]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.subject_id) return;

    try {
      await domain.createHomeworkUseCase.execute({
        ...formData,
        user_id: "current-user", // Esto debería ser reemplazado por el ID del usuario actual
      });
      router.push("/homeworks");
    } catch (err) {
      console.error("Error creating homework:", err);
      setError("Error al crear el trabajo");
    }
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <p>Cargando...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <p className={styles.error}>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <button onClick={() => router.back()} className={styles.backButton}>
        ← Volver
      </button>
      <div className={styles.formCard}>
        <div className={styles.header}>
          <h1 className={styles.title}>Nuevo trabajo</h1>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="subject_id" className={styles.label}>
              Asignatura
            </label>
            <select
              id="subject_id"
              name="subject_id"
              value={formData.subject_id}
              onChange={handleChange}
              className={styles.select}
              required
            >
              {subjects.length === 0 ? (
                <option value="">No hay asignaturas disponibles</option>
              ) : (
                subjects.map((subject) => (
                  <option key={subject.id} value={subject.id}>
                    {subject.name} ({subject.course.title})
                  </option>
                ))
              )}
            </select>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="title" className={styles.label}>
              Título
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description" className={styles.label}>
              Descripción
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className={styles.input}
              rows={4}
              required
            />
          </div>

          <div className={styles.formActions}>
            <button type="submit" className={styles.submitButton}>
              Crear trabajo
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
