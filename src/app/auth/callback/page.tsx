"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function AuthCallbackPage() {
  const router = useRouter();

  useEffect(() => {
    const redirectTo = localStorage.getItem("redirectTo");
    console.log("AuthCallbackPage - redirectTo:", redirectTo);

    localStorage.removeItem("redirectTo");

    if (redirectTo) {
      console.log(`AuthCallbackPage - Redirigiendo a: ${redirectTo}`);
      router.push(redirectTo);
    } else {
      console.log("AuthCallbackPage - Redirigiendo a la página principal");
      router.push("/");
    }
  }, [router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <h1 className="text-2xl font-bold mb-4">Autenticando...</h1>
      <p>Por favor, espera mientras te redirigimos.</p>
    </div>
  );
}
