import { SupabaseClient } from "@supabase/supabase-js";
import { HttpRepository } from "../repositories";

export interface IndicatorsUseCaseDependencies {
  repository: HttpRepository;
}

export interface IndicatorLevel {
  id?: string;
  level_number: number;
  title: string;
  created_at?: string;
  updated_at?: string;
}

export interface Indicator {
  id: string;
  title: string;
  criteria_id: string;
  slug: string;
  created_at: string;
  updated_at: string;
  levels: IndicatorLevel[];
  criteria?: {
    id: string;
    name: string;
    slug: string;
    subject?: {
      id: string;
      name: string;
      slug: string;
    };
  };
}

export interface CreateIndicatorParams {
  title: string;
  criteria_id: string;
  slug: string;
  levels: IndicatorLevel[];
}

export interface UpdateIndicatorParams {
  id: string;
  title: string;
  slug: string;
  levels: IndicatorLevel[];
}

export interface HttpRepositoryDependencies {
  fetcher: SupabaseClient;
}
