import { HttpRepository } from "../repositories";
import {
  IndicatorsUseCaseDependencies,
  Indicator,
  UpdateIndicatorParams,
} from "../types";

export class UpdateIndicatorUseCase {
  private _repository: HttpRepository;

  constructor({ repository }: IndicatorsUseCaseDependencies) {
    this._repository = repository;
  }

  static create() {
    const repository = HttpRepository.create();
    return new UpdateIndicatorUseCase({ repository });
  }

  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  }

  async execute(params: UpdateIndicatorParams): Promise<Indicator> {
    try {
      const slug = this.generateSlug(params.title);
      return await this._repository.updateIndicator({ ...params, slug });
    } catch (error) {
      console.error("Error en UpdateIndicatorUseCase.execute:", error);
      throw new Error(
        `Error al actualizar el indicador: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
