import { HttpRepository } from "../repositories";
import type {
  IndicatorsUseCaseDependencies,
  CreateIndicatorParams,
  Indicator,
} from "../types";

export class CreateIndicatorUseCase {
  private readonly _repository: HttpRepository;

  constructor({ repository }: IndicatorsUseCaseDependencies) {
    this._repository = repository;
  }

  static create() {
    const repository = HttpRepository.create();
    return new CreateIndicatorUseCase({ repository });
  }

  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  }

  async execute(params: {
    title: string;
    criteria_id: string;
    levels: Array<{
      level_number: number;
      title: string;
    }>;
  }): Promise<Indicator> {
    try {
      const slug = this.generateSlug(params.title);
      const createParams: CreateIndicatorParams = {
        title: params.title,
        criteria_id: params.criteria_id,
        slug,
        levels: params.levels,
      };
      return await this._repository.createIndicator(createParams);
    } catch (error) {
      console.error("Error en CreateIndicatorUseCase.execute:", error);
      throw new Error(
        `Error al crear el indicador: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
