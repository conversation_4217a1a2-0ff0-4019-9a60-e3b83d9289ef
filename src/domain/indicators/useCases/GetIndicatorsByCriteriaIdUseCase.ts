import { HttpRepository } from "../repositories";
import { IndicatorsUseCaseDependencies, Indicator } from "../types";

export class GetIndicatorsByCriteriaIdUseCase {
  private _repository: HttpRepository;

  constructor({ repository }: IndicatorsUseCaseDependencies) {
    this._repository = repository;
  }

  static create() {
    const repository = HttpRepository.create();
    return new GetIndicatorsByCriteriaIdUseCase({ repository });
  }

  async execute(criteriaId: string): Promise<Indicator[]> {
    try {
      return await this._repository.getIndicatorsByCriteriaId(criteriaId);
    } catch (error) {
      console.error(
        "Error en GetIndicatorsByCriteriaIdUseCase.execute:",
        error
      );
      throw new Error(
        `Error al obtener los indicadores: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
