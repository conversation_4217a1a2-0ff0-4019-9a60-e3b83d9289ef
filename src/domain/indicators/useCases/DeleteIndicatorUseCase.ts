import { HttpRepository } from "../repositories";
import { IndicatorsUseCaseDependencies } from "../types";

export interface DeleteIndicatorParams {
  indicatorId: string;
}

export class DeleteIndicatorUseCase {
  private _repository: HttpRepository;

  constructor({ repository }: IndicatorsUseCaseDependencies) {
    this._repository = repository;
  }

  static async create(): Promise<DeleteIndicatorUseCase> {
    const repository = HttpRepository.create();
    return new DeleteIndicatorUseCase({ repository });
  }

  async execute({ indicatorId }: DeleteIndicatorParams): Promise<void> {
    try {
      await this._repository.deleteIndicator(indicatorId);
    } catch (error) {
      console.error("Error en DeleteIndicatorUseCase.execute:", error);
      throw new Error(
        `Error al eliminar el indicador: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
