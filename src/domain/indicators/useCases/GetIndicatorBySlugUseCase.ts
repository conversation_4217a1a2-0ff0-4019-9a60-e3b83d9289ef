import { HttpRepository } from "../repositories";
import { IndicatorsUseCaseDependencies, Indicator } from "../types";

export class GetIndicatorBySlugUseCase {
  private _repository: HttpRepository;

  constructor({ repository }: IndicatorsUseCaseDependencies) {
    this._repository = repository;
  }

  static create() {
    const repository = HttpRepository.create();
    return new GetIndicatorBySlugUseCase({ repository });
  }

  async execute(slug: string): Promise<Indicator | null> {
    try {
      return await this._repository.getIndicatorBySlug(slug);
    } catch (error) {
      console.error("Error en GetIndicatorBySlugUseCase.execute:", error);
      throw new Error(
        `Error al obtener el indicador: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
