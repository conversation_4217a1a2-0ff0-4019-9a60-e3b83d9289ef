import { supabase } from "@/utils/supabase/client";
import { SupabaseClient } from "@supabase/supabase-js";
import {
  HttpRepositoryDependencies,
  Indicator,
  CreateIndicatorParams,
  UpdateIndicatorParams,
  IndicatorLevel,
} from "../types";

export class HttpRepository {
  private _fetcher: SupabaseClient;

  static create(): HttpRepository {
    return new HttpRepository({ fetcher: supabase });
  }

  constructor({ fetcher }: HttpRepositoryDependencies) {
    this._fetcher = fetcher;
  }

  async createIndicator(params: CreateIndicatorParams): Promise<Indicator> {
    try {
      const { data: indicator, error: indicatorError } = await this._fetcher
        .from("indicators")
        .insert([
          {
            title: params.title,
            criteria_id: params.criteria_id,
            slug: params.slug,
          },
        ])
        .select()
        .single();

      if (indicatorError) {
        throw new Error(
          `Error al crear el indicador: ${indicatorError.message}`
        );
      }

      // Crear los niveles del indicador
      const { error: levelsError } = await this._fetcher
        .from("indicator_levels")
        .insert(
          params.levels.map((level) => ({
            indicator_id: indicator.id,
            level_number: level.level_number,
            title: level.title,
          }))
        );

      if (levelsError) {
        throw new Error(`Error al crear los niveles: ${levelsError.message}`);
      }

      return {
        ...indicator,
        levels: params.levels,
      };
    } catch (error) {
      console.error("Error en HttpRepository.createIndicator:", error);
      throw new Error(
        `Error al crear el indicador: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  async getIndicatorsByCriteriaId(criteriaId: string): Promise<Indicator[]> {
    try {
      const { data: indicators, error: indicatorsError } = await this._fetcher
        .from("indicators")
        .select(
          `
          *,
          levels:indicator_levels (
            id,
            level_number,
            title,
            created_at,
            updated_at
          )
        `
        )
        .eq("criteria_id", criteriaId)
        .order("created_at", { ascending: true });

      if (indicatorsError) {
        throw new Error(
          `Error al obtener los indicadores: ${indicatorsError.message}`
        );
      }

      return indicators || [];
    } catch (error) {
      console.error(
        "Error en HttpRepository.getIndicatorsByCriteriaId:",
        error
      );
      throw new Error(
        `Error al obtener los indicadores: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  async getIndicatorBySlug(slug: string): Promise<Indicator | null> {
    try {
      const { data, error } = await this._fetcher
        .from("indicators")
        .select(
          `
          *,
          levels:indicator_levels (
            id,
            level_number,
            title,
            created_at,
            updated_at
          ),
          criteria:criteria_id (
            id,
            name,
            slug,
            subject:subject_id (
              id,
              name,
              slug
            )
          )
        `
        )
        .eq("slug", slug)
        .single();

      if (error) {
        console.error("Error fetching indicator by slug:", error);
        throw new Error("Error al obtener el indicador");
      }

      return data;
    } catch (error) {
      console.error("Error en HttpRepository.getIndicatorBySlug:", error);
      throw new Error(
        `Error al obtener el indicador: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  async updateIndicator(params: UpdateIndicatorParams): Promise<Indicator> {
    try {
      // Update the indicator
      const { data: updatedIndicator, error: indicatorError } =
        await this._fetcher
          .from("indicators")
          .update({
            title: params.title,
            slug: params.slug,
          })
          .eq("id", params.id)
          .select()
          .single();

      if (indicatorError) {
        throw new Error(
          `Error al actualizar el indicador: ${indicatorError.message}`
        );
      }

      // Eliminar los niveles existentes
      const { error: deleteError } = await this._fetcher
        .from("indicator_levels")
        .delete()
        .eq("indicator_id", params.id);

      if (deleteError) {
        throw new Error(
          `Error al eliminar los niveles: ${deleteError.message}`
        );
      }

      // Crear los nuevos niveles
      const { error: levelsError } = await this._fetcher
        .from("indicator_levels")
        .insert(
          params.levels.map((level) => ({
            indicator_id: params.id,
            level_number: level.level_number,
            title: level.title,
          }))
        );

      if (levelsError) {
        throw new Error(`Error al crear los niveles: ${levelsError.message}`);
      }

      // Fetch the complete indicator data with criteria and subject
      const { data: completeIndicator, error: fetchError } = await this._fetcher
        .from("indicators")
        .select(
          `
          *,
          levels:indicator_levels (
            id,
            level_number,
            title,
            created_at,
            updated_at
          ),
          criteria:criteria_id (
            id,
            name,
            slug,
            subject:subject_id (
              id,
              name,
              slug
            )
          )
        `
        )
        .eq("id", params.id)
        .single();

      if (fetchError) {
        console.error("Error fetching updated indicator:", fetchError);
        // If we can't fetch the complete data, return what we have
        return {
          ...updatedIndicator,
          levels: params.levels,
        };
      }

      return completeIndicator;
    } catch (error) {
      console.error("Error en HttpRepository.updateIndicator:", error);
      throw new Error(
        `Error al actualizar el indicador: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  async deleteIndicator(indicatorId: string): Promise<void> {
    try {
      const { error } = await this._fetcher
        .from("indicators")
        .delete()
        .eq("id", indicatorId);

      if (error) {
        throw new Error(`Error al eliminar el indicador: ${error.message}`);
      }
    } catch (error) {
      console.error("Error en HttpRepository.deleteIndicator:", error);
      throw error;
    }
  }
}
