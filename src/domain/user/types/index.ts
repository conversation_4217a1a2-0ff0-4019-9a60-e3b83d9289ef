import { User, Session } from "@supabase/supabase-js";
export interface UserRepository {
  getSession(): Promise<{ session: Session | null; error: Error | null }>;
  refreshSession(): Promise<{
    session: Session | null;
    user: User | null;
    error: Error | null;
  }>;
  getUser(): Promise<{ user: User | null; error: Error | null }>;
  signInWithGoogle(redirectTo?: string): Promise<{ error: Error | null }>;
  signOut(): Promise<{ error: Error | null }>;
  onAuthStateChange(callback: (event: any, session: Session | null) => void): {
    data: { subscription: { unsubscribe: () => void } };
  };
}
export interface GetSessionUseCaseDependencies {
  repository: UserRepository;
}

export interface RefreshSessionUseCaseDependencies {
  repository: UserRepository;
}

export interface GetUserUseCaseDependencies {
  repository: UserRepository;
}

export interface SignInWithGoogleUseCaseDependencies {
  repository: UserRepository;
}

export interface SignOutUseCaseDependencies {
  repository: UserRepository;
}

export interface IsAuthenticatedUseCaseDependencies {
  repository: UserRepository;
}

export interface OnAuthStateChangeUseCaseDependencies {
  repository: UserRepository;
}

export interface OnAuthStateChangeParams {
  callback: (
    event:
      | "INITIAL_SESSION"
      | "SIGNED_IN"
      | "SIGNED_OUT"
      | "TOKEN_REFRESHED"
      | "USER_UPDATED"
      | "PASSWORD_RECOVERY",
    session: Session | null
  ) => void;
}
