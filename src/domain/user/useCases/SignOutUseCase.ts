import { HttpRepository } from "../repositories";
import { SignOutUseCaseDependencies, UserRepository } from "../types";

export class SignOutUseCase {
  private readonly _repository: UserRepository;

  static create(): SignOutUseCase {
    const repository = HttpRepository.create();
    return new SignOutUseCase({ repository });
  }

  constructor({ repository }: SignOutUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(): Promise<{ error: Error | null }> {
    return await this._repository.signOut();
  }
}
