import { Session, User } from "@supabase/supabase-js";
import { HttpRepository } from "../repositories";
import { RefreshSessionUseCaseDependencies, UserRepository } from "../types";

export class RefreshSessionUseCase {
  private readonly _repository: UserRepository;

  static create(): RefreshSessionUseCase {
    const repository = HttpRepository.create();
    return new RefreshSessionUseCase({ repository });
  }

  constructor({ repository }: RefreshSessionUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(): Promise<{
    session: Session | null;
    user: User | null;
    error: Error | null;
  }> {
    return await this._repository.refreshSession();
  }
}
