import { Session } from "@supabase/supabase-js";
import { HttpRepository } from "../repositories";
import { GetSessionUseCaseDependencies, UserRepository } from "../types";

export class GetSessionUseCase {
  private readonly _repository: UserRepository;

  static create(): GetSessionUseCase {
    const repository = HttpRepository.create();
    return new GetSessionUseCase({ repository });
  }

  constructor({ repository }: GetSessionUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(): Promise<{
    session: Session | null;
    error: Error | null;
  }> {
    return await this._repository.getSession();
  }
}
