import { HttpRepository } from "../repositories";
import {
  OnAuthStateChangeParams,
  OnAuthStateChangeUseCaseDependencies,
  UserRepository,
} from "../types";

export class OnAuthStateChangeUseCase {
  private readonly _repository: UserRepository;

  static create(): OnAuthStateChangeUseCase {
    const repository = HttpRepository.create();
    return new OnAuthStateChangeUseCase({ repository });
  }

  constructor({ repository }: OnAuthStateChangeUseCaseDependencies) {
    this._repository = repository;
  }

  execute({ callback }: OnAuthStateChangeParams): {
    data: { subscription: { unsubscribe: () => void } };
  } {
    return this._repository.onAuthStateChange(callback);
  }
}
