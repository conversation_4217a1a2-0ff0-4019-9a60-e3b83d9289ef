import { HttpRepository } from "../repositories";
import { SignInWithGoogleUseCaseDependencies, UserRepository } from "../types";

export class SignInWithGoogleUseCase {
  private readonly _repository: UserRepository;

  static create(): SignInWithGoogleUseCase {
    const repository = HttpRepository.create();
    return new SignInWithGoogleUseCase({ repository });
  }

  constructor({ repository }: SignInWithGoogleUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(redirectTo?: string): Promise<{ error: Error | null }> {
    return await this._repository.signInWithGoogle(redirectTo);
  }
}
