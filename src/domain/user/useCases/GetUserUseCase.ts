import { User } from "@supabase/supabase-js";
import { HttpRepository } from "../repositories";
import { GetUserUseCaseDependencies, UserRepository } from "../types";

export class GetUserUseCase {
  private readonly _repository: UserRepository;

  static create(): GetUserUseCase {
    const repository = HttpRepository.create();
    return new GetUserUseCase({ repository });
  }

  constructor({ repository }: GetUserUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(): Promise<{ user: User | null; error: Error | null }> {
    return await this._repository.getUser();
  }
}
