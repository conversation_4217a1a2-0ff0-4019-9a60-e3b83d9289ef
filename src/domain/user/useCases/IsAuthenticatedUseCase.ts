import { Session } from "@supabase/supabase-js";
import { HttpRepository } from "../repositories";
import { IsAuthenticatedUseCaseDependencies, UserRepository } from "../types";

export class IsAuthenticatedUseCase {
  private readonly _repository: UserRepository;

  static create(): IsAuthenticatedUseCase {
    const repository = HttpRepository.create();
    return new IsAuthenticatedUseCase({ repository });
  }

  constructor({ repository }: IsAuthenticatedUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(): Promise<{
    isAuthenticated: boolean;
    session: Session | null;
    error: Error | null;
  }> {
    const { session, error } = await this._repository.getSession();
    return { isAuthenticated: !!session, session, error };
  }
}
