import { User, Session } from "@supabase/supabase-js";
import { supabase, getBaseUrl } from "@/utils/supabase/client";
import { UserRepository } from "../types";

export class HttpRepository implements UserRepository {
  static create(): HttpRepository {
    return new HttpRepository();
  }

  async getSession(): Promise<{
    session: Session | null;
    error: Error | null;
  }> {
    try {
      const { data, error } = await supabase.auth.getSession();
      console.log(
        "UserRepository - getSession:",
        data.session ? "Sesión activa" : "Sin sesión"
      );
      return { session: data.session, error: error };
    } catch (error) {
      console.error("Error inesperado al obtener la sesión:", error);
      return { session: null, error: error as Error };
    }
  }

  async refreshSession(): Promise<{
    session: Session | null;
    user: User | null;
    error: Error | null;
  }> {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      console.log(
        "UserRepository - refreshSession:",
        data.session ? "Sesión refrescada" : "No se pudo refrescar"
      );
      return {
        session: data.session,
        user: data.user,
        error: error,
      };
    } catch (error) {
      console.error("Error inesperado al refrescar el token:", error);
      return {
        session: null,
        user: null,
        error: error as Error,
      };
    }
  }

  async getUser(): Promise<{ user: User | null; error: Error | null }> {
    try {
      const { data, error } = await supabase.auth.getUser();
      return { user: data.user, error: error };
    } catch (error) {
      return { user: null, error: error as Error };
    }
  }

  async signInWithGoogle(
    redirectTo?: string
  ): Promise<{ error: Error | null }> {
    try {
      console.log("UserRepository - Iniciando sesión con Google");
      console.log(
        "UserRepository - redirectTo:",
        redirectTo ?? `${window.location.origin}/auth/callback`
      );
      // Si hay un redirectTo, lo guardamos en localStorage para recuperarlo después
      if (redirectTo && typeof window !== "undefined") {
        localStorage.setItem("redirectTo", redirectTo);
      }

      // No necesitamos especificar una URL de redirección aquí porque ya está configurada
      // en el cliente de Supabase en src/utils/supabase/client.ts

      const { error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: getBaseUrl(),
          scopes: redirectTo ? "email profile" : undefined,
          queryParams: {
            access_type: "offline",
            prompt: "consent",
          },
        },
      });
      if (error) {
        console.error(
          "UserRepository - Error al iniciar sesión con Google:",
          error
        );
      }
      return { error };
    } catch (error) {
      console.error(
        "UserRepository - Error inesperado al iniciar sesión con Google:",
        error
      );
      return { error: error as Error };
    }
  }

  async signOut(): Promise<{ error: Error | null }> {
    try {
      const { error } = await supabase.auth.signOut();
      return { error };
    } catch (error) {
      return { error: error as Error };
    }
  }

  onAuthStateChange(callback: (event: any, session: Session | null) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }
}
