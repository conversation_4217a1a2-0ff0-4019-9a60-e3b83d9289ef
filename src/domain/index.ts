import * as coursesUseCases from "./courses/useCases";
import * as subjectsUseCases from "./subjects/useCases";
import * as criteriasUseCases from "./criterias/useCases";
import * as studentsUseCases from "./students/useCases";
import * as indicatorsUseCases from "./indicators/useCases";
import * as homeworksUseCases from "./homeworks/useCases";
import * as evaluationsUseCases from "./evaluations/useCases";
import * as userUseCases from "./user/useCases";

const useCases = {
  ...coursesUseCases,
  ...subjectsUseCases,
  ...criteriasUseCases,
  ...studentsUseCases,
  ...indicatorsUseCases,
  ...homeworksUseCases,
  ...evaluationsUseCases,
  ...userUseCases,
};

export class DomainApp {
  static create() {
    return new DomainApp();
  }

  [key: string]: any;

  constructor() {
    Object.entries(useCases).forEach(([key, value]) => {
      const useCaseName = key.charAt(0).toLowerCase() + key.slice(1);
      this[useCaseName] = this._getter(value);
    });
  }

  private _getter(useCase: {
    create: () =>
      | Promise<{ execute: (...args: any[]) => any }>
      | { execute: (...args: any[]) => any };
  }): { execute: (...args: any[]) => Promise<any> } {
    return {
      async execute(...args: any[]) {
        // Manejar tanto los casos que devuelven una promesa como los que no
        const instance = await Promise.resolve(useCase.create());
        return instance.execute(...args);
      },
    };
  }
}
