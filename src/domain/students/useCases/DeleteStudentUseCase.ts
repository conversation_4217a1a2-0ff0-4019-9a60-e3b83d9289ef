import { HttpRepository } from "@/domain/courses/repositories/HttpRepository";

interface DeleteStudentUseCaseDependencies {
  repository: HttpRepository;
}

interface DeleteStudentParams {
  studentId: string;
}

export class DeleteStudentUseCase {
  private _repository: HttpRepository;

  static create(): DeleteStudentUseCase {
    return new DeleteStudentUseCase({
      repository: HttpRepository.create(),
    });
  }

  constructor({ repository }: DeleteStudentUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({ studentId }: DeleteStudentParams): Promise<void> {
    try {
      const { error } = await this._repository.fetcher
        .from("students")
        .delete()
        .eq("id", studentId);

      if (error) {
        console.error("Error al eliminar el alumno:", error);
        throw new Error(error.message);
      }
    } catch (error) {
      console.error("Error en DeleteStudentUseCase:", error);
      throw error;
    }
  }
}
