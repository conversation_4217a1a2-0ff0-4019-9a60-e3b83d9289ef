import { HttpRepository } from "@/domain/courses/repositories/HttpRepository";

interface Student {
  id: string;
  first_name: string;
  last_name1: string;
  last_name2?: string;
  is_pi: boolean;
  slug: string;
  created_at: string;
  updated_at: string;
  course?: {
    id: string;
    title: string;
    description: string;
    slug: string;
  };
}

interface GetStudentsListUseCaseDependencies {
  repository: HttpRepository;
}

export class GetStudentsListUseCase {
  private _repository: HttpRepository;

  static create(): GetStudentsListUseCase {
    return new GetStudentsListUseCase({
      repository: HttpRepository.create(),
    });
  }

  constructor({ repository }: GetStudentsListUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(): Promise<Student[]> {
    try {
      const { data: students, error } = await this._repository.fetcher
        .from("students")
        .select(
          `
          *,
          course:student_courses!inner(
            course:course_id(
              id,
              title,
              description,
              slug
            )
          )
        `
        )
        .order("first_name", { ascending: true })
        .order("last_name1", { ascending: true })
        .order("last_name2", { ascending: true });

      if (error) {
        console.error("Error al obtener la lista de alumnos:", error);
        return [];
      }

      // Transformar la respuesta para tener un formato más limpio
      return students.map((student) => ({
        ...student,
        course: student.course[0]?.course,
      }));
    } catch (error) {
      console.error("Error en GetStudentsListUseCase:", error);
      return [];
    }
  }
}
