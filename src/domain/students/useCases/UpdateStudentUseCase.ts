import { supabase } from "@/utils/supabase/client";

interface UpdateStudentParams {
  id: string;
  first_name: string;
  last_name1: string;
  last_name2?: string;
  is_pi: boolean;
}

export class UpdateStudentUseCase {
  static create() {
    return new UpdateStudentUseCase();
  }

  async execute(params: UpdateStudentParams) {
    const { id, first_name, last_name1, last_name2, is_pi } = params;

    const { data, error } = await supabase
      .from("students")
      .update({
        first_name,
        last_name1,
        last_name2,
        is_pi,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select()
      .single();

    if (error) {
      throw new Error(`Error al actualizar el alumno: ${error.message}`);
    }

    return data;
  }
}
