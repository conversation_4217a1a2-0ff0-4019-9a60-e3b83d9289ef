import { HttpRepository } from "@/domain/courses/repositories/HttpRepository";

interface Student {
  id: string;
  first_name: string;
  last_name1: string;
  last_name2?: string;
  is_pi: boolean;
  slug: string;
  created_at: string;
  updated_at: string;
  courses?: {
    course: {
      id: string;
      title: string;
      description: string;
      slug: string;
      subjects?: {
        id: string;
        name: string;
        slug: string;
        created_at: string;
        updated_at: string;
      }[];
    };
  }[];
}

interface GetStudentBySlugUseCaseDependencies {
  repository: HttpRepository;
}

interface GetStudentBySlugParams {
  slug: string;
}

export class GetStudentBySlugUseCase {
  private _repository: HttpRepository;

  static create(): GetStudentBySlugUseCase {
    return new GetStudentBySlugUseCase({
      repository: HttpRepository.create(),
    });
  }

  constructor({ repository }: GetStudentBySlugUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({
    slug,
  }: GetStudentBySlugParams): Promise<Student | undefined> {
    try {
      if (!slug) {
        console.error("Slug no proporcionado");
        throw new Error(
          "El identificador del alumno es inválido. Por favor, vuelve a la lista de alumnos e intenta nuevamente."
        );
      }

      const { data: student, error } = await this._repository.fetcher
        .from("students")
        .select(
          `
          *,
          courses:student_courses!inner(
            course:course_id(
              id,
              title,
              description,
              slug,
              subjects:subjects(
                id,
                name,
                slug,
                created_at,
                updated_at
              )
            )
          )
        `
        )
        .eq("slug", slug)
        .single();

      if (error) {
        console.error("Error al obtener el alumno:", error);

        if (error.code === "PGRST116") {
          throw new Error(
            "No se encontró el alumno con el identificador proporcionado"
          );
        }

        throw new Error(`Error al obtener datos del alumno: ${error.message}`);
      }

      if (!student) {
        throw new Error(
          "No se encontró el alumno con el identificador proporcionado"
        );
      }

      return student;
    } catch (error) {
      console.error("Error en GetStudentBySlugUseCase:", error);
      throw error;
    }
  }
}
