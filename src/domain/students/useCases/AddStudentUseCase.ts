import { HttpRepository } from "@/domain/courses/repositories/HttpRepository";
import { slugify } from "@/utils/slugify";

interface Student {
  id: string;
  first_name: string;
  last_name1: string;
  last_name2?: string;
  is_pi: boolean;
  slug: string;
  created_at: string;
  updated_at: string;
}

interface AddStudentUseCaseDependencies {
  repository: HttpRepository;
}

interface AddStudentParams {
  first_name: string;
  last_name1: string;
  last_name2?: string;
  is_pi: boolean;
  course_id: string;
}

export class AddStudentUseCase {
  private _repository: HttpRepository;

  static create(): AddStudentUseCase {
    return new AddStudentUseCase({
      repository: HttpRepository.create(),
    });
  }

  constructor({ repository }: AddStudentUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(params: AddStudentParams): Promise<Student | undefined> {
    try {
      // Generar un slug para el estudiante
      const fullName = `${params.first_name} ${params.last_name1}${
        params.last_name2 ? ` ${params.last_name2}` : ""
      }`;
      const slug = slugify(fullName);

      // Verificar si el slug ya existe
      const { data: existingStudent } = await this._repository.fetcher
        .from("students")
        .select("slug")
        .eq("slug", slug)
        .maybeSingle();

      // Si el slug ya existe, añadimos un sufijo numérico
      let finalSlug = slug;
      if (existingStudent) {
        // Buscar todos los slugs similares para determinar el siguiente sufijo
        const { data: similarSlugs } = await this._repository.fetcher
          .from("students")
          .select("slug")
          .like("slug", `${slug}-%`);

        const suffixes = (similarSlugs || [])
          .map((s) => {
            const match = s.slug.match(`${slug}-(\\d+)$`);
            return match ? parseInt(match[1], 10) : 0;
          })
          .filter((n) => !isNaN(n));

        const nextSuffix = suffixes.length > 0 ? Math.max(...suffixes) + 1 : 1;
        finalSlug = `${slug}-${nextSuffix}`;
      }

      // Iniciar una transacción
      const { data: student, error: studentError } =
        await this._repository.fetcher.rpc("create_student_with_course", {
          p_first_name: params.first_name,
          p_last_name1: params.last_name1,
          p_last_name2: params.last_name2,
          p_is_pi: params.is_pi,
          p_slug: finalSlug,
          p_course_id: params.course_id,
        });

      if (studentError) {
        console.error(
          "Error al crear el alumno y asignarlo al curso:",
          studentError
        );
        throw new Error(`Error al crear el alumno: ${studentError.message}`);
      }

      return student;
    } catch (error) {
      console.error("Error en AddStudentUseCase:", error);
      throw error;
    }
  }
}
