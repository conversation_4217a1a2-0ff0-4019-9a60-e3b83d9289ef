import { supabase } from "@/utils/supabase/client";

interface AssignStudentToCourseParams {
  studentId: string;
  courseId: string;
  remove?: boolean;
}

export class AssignStudentToCourseUseCase {
  static create() {
    return new AssignStudentToCourseUseCase();
  }

  async execute(params: AssignStudentToCourseParams) {
    const { studentId, courseId, remove } = params;

    if (remove) {
      // Eliminar la relación
      const { error } = await supabase
        .from("student_courses")
        .delete()
        .eq("student_id", studentId)
        .eq("course_id", courseId);

      if (error) {
        throw new Error(`Error al eliminar la asignación: ${error.message}`);
      }

      return;
    }

    // Verificar si la relación ya existe
    const { data: existingRelation, error: checkError } = await supabase
      .from("student_courses")
      .select()
      .eq("student_id", studentId)
      .eq("course_id", courseId)
      .single();

    if (checkError && checkError.code !== "PGRST116") {
      throw new Error(
        `Error al verificar la asignación: ${checkError.message}`
      );
    }

    // Si la relación ya existe, no hacemos nada
    if (existingRelation) {
      return;
    }

    // Crear la nueva relación
    const { error: insertError } = await supabase
      .from("student_courses")
      .insert([
        {
          student_id: studentId,
          course_id: courseId,
        },
      ]);

    if (insertError) {
      throw new Error(`Error al asignar el curso: ${insertError.message}`);
    }
  }
}
