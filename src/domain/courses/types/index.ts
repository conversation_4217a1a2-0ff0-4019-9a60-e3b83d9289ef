import { HttpRepository } from "../repositories";

interface AddCourseUseCaseDependencies {
  repository: HttpRepository;
}

interface AddCourseParams {
  title: string;
  description: string;
  user_id: string;
}

interface DeleteCourseUseCaseDependencies {
  repository: HttpRepository;
}

interface DeleteCourseParams {
  courseId: string;
}

interface Course {
  id: string;
  description: string;
  title: string;
  user_id: string;
  slug: string;
}

interface Subject {
  id: string;
  name: string;
  slug: string;
}

interface CourseWithSubjects extends Course {
  subjects: Subject[];
}

interface CourseList {
  id: string;
  title: string;
  description: string;
  user_id: string;
  slug: string;
}

interface GetCoursesListUseCaseDependencies {
  repository: HttpRepository;
}

interface GetCourseBySlugUseCaseDependencies {
  repository: HttpRepository;
}

interface GetCourseBySlugParams {
  slug: string;
}

interface GetCourseInfoUseCaseDependencies {
  repository: HttpRepository;
}

interface GetCourseInfoParams {
  courseSlug: string;
}

interface CourseInfo {
  id: string;
  title: string;
  description: string;
  user_id: string;
  subjects: Array<{
    id: string;
    name: string;
  }>;
}

export type {
  AddCourseParams,
  AddCourseUseCaseDependencies,
  Course,
  CourseInfo,
  CourseList,
  DeleteCourseParams,
  DeleteCourseUseCaseDependencies,
  GetCourseBySlugParams,
  GetCourseBySlugUseCaseDependencies,
  GetCourseInfoParams,
  GetCourseInfoUseCaseDependencies,
  GetCoursesListUseCaseDependencies,
  Subject,
  CourseWithSubjects,
};
