import { HttpRepository } from "../repositories";
import {
  CourseInfo,
  GetCourseInfoParams,
  GetCourseInfoUseCaseDependencies,
} from "../types";

export class GetCourseInfoUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetCourseInfoUseCase {
    const repository = HttpRepository.create();
    return new GetCourseInfoUseCase({ repository });
  }

  constructor({ repository }: GetCourseInfoUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({
    courseSlug,
  }: GetCourseInfoParams): Promise<CourseInfo | undefined> {
    return await this._repository.getCourseBySlug(courseSlug);
  }
}
