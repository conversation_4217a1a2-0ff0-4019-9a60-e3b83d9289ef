import { HttpRepository } from "../repositories";
import {
  Course,
  GetCourseBySlugParams,
  GetCourseBySlugUseCaseDependencies,
} from "../types";

export class GetCourseBySlugUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetCourseBySlugUseCase {
    const repository = HttpRepository.create();
    return new GetCourseBySlugUseCase({ repository });
  }

  constructor({ repository }: GetCourseBySlugUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({ slug }: GetCourseBySlugParams): Promise<Course | undefined> {
    try {
      const course = await this._repository.getCourseBySlug(slug);
      return course;
    } catch (error) {
      console.error("Error en GetCourseBySlugUseCase:", error);
      throw error;
    }
  }
}
