import { HttpRepository } from "../repositories";
import type { AddCourseParams, AddCourseUseCaseDependencies } from "../types";

export class AddCourseUseCase {
  private readonly _repository: HttpRepository;

  static create(): AddCourseUseCase {
    const repository = HttpRepository.create();
    return new AddCourseUseCase({ repository });
  }

  constructor({ repository }: AddCourseUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({ title, description, user_id }: AddCourseParams) {
    const course = await this._repository.addCourse({
      title,
      description,
      user_id,
    });
    if (!course) {
      throw new Error("Error al crear el curso");
    }
    return course;
  }
}
