import { HttpRepository } from "../repositories/HttpRepository";
import { Course } from "../types";

interface GetCoursesListUseCaseDependencies {
  repository: HttpRepository;
}

export class GetCoursesListUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetCoursesListUseCase {
    const repository = HttpRepository.create();
    return new GetCoursesListUseCase({ repository });
  }

  constructor({ repository }: GetCoursesListUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(userId: string): Promise<Course[]> {
    if (!userId) {
      throw new Error("El ID del usuario es requerido");
    }

    try {
      const data = await this._repository.getCoursesList(userId);
      console.log("Datos recibidos del repositorio:", data);
      return data;
    } catch (error) {
      console.error("Error en GetCoursesListUseCase:", error);
      throw error;
    }
  }
}
