import { HttpRepository } from "../repositories";
import type {
  DeleteCourseParams,
  DeleteCourseUseCaseDependencies,
} from "../types";

export class DeleteCourseUseCase {
  private readonly _repository: HttpRepository;

  static create(): DeleteCourseUseCase {
    const repository = HttpRepository.create();
    return new DeleteCourseUseCase({ repository });
  }

  constructor({ repository }: DeleteCourseUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({ courseId }: DeleteCourseParams): Promise<void> {
    await this._repository.deleteCourse({ courseId });
  }
}
