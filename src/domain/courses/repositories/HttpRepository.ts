import { SupabaseClient } from "@supabase/supabase-js";
import { Course, CourseWithSubjects, Subject } from "../types";
import { slugify } from "@/utils/slugify";
import { CourseRepository } from "./CourseRepository";
import { supabase } from "@/utils/supabase/client";

export interface HttpRepositoryDependencies {
  fetcher: SupabaseClient;
}

interface AddSubjectParams {
  name: string;
  courseID: string;
}

interface AddCourseParams {
  title: string;
  description: string;
  user_id: string;
}

interface DeleteCourseParams {
  courseId: string;
}

export class HttpRepository implements CourseRepository {
  static create(): HttpRepository {
    return new HttpRepository(supabase);
  }

  constructor(private _fetcher: HttpRepositoryDependencies["fetcher"]) {}

  get fetcher(): SupabaseClient {
    return this._fetcher;
  }

  async getCoursesList(userId: string): Promise<Course[]> {
    console.log(
      "Iniciando getCoursesList en HttpRepository para usuario:",
      userId
    );
    try {
      console.log("Haciendo llamada a Supabase...");
      const { data: courses, error } = await this._fetcher
        .from("courses")
        .select("*")
        .eq("user_id", userId)
        .order("description");

      if (error) {
        console.error("Error al obtener cursos:", error);
        throw new Error(error.message || "Error al obtener los cursos");
      }

      console.log("Respuesta de Supabase:", { courses, error });
      return courses || [];
    } catch (error) {
      console.error("Error en getCoursesList:", error);
      throw error;
    }
  }

  async getCourseInfoByID({
    courseID,
  }: {
    courseID: string;
  }): Promise<CourseWithSubjects | undefined> {
    const { data: course, error } = await this._fetcher
      .from("courses")
      .select(`*,subjects(id,name)`)
      .eq("id", courseID);
    if (error) {
      console.error(error);
      return undefined;
    } else {
      return course[0];
    }
  }

  async getCourseBySlug(slug: string): Promise<CourseWithSubjects | undefined> {
    const { data: course, error } = await this._fetcher
      .from("courses")
      .select(
        `
        *,
        subjects(
          id,
          name,
          slug
        )
      `
      )
      .eq("slug", slug)
      .single();

    if (error) {
      console.error(error);
      return undefined;
    }

    return course;
  }

  async addSubjectToCourse({
    name,
    courseID,
  }: AddSubjectParams): Promise<
    { subject: Subject; name: string } | undefined
  > {
    const { data: subjects, error } = await this._fetcher
      .from("subjects")
      .insert([{ name, courseID }])
      .select("*");

    if (error) {
      console.error(error);
      return undefined;
    } else if (!subjects || subjects.length === 0) {
      return undefined;
    } else {
      return { subject: subjects[0], name };
    }
  }

  async addCourse({
    title,
    description,
    user_id,
  }: AddCourseParams): Promise<Course | undefined> {
    const slug = slugify(title);
    console.log("Intentando crear curso con:", {
      title,
      description,
      user_id,
      slug,
    });

    try {
      const { data: course, error } = await this._fetcher
        .from("courses")
        .insert([
          {
            title,
            description,
            user_id,
            slug,
            created_at: new Date().toISOString(),
          },
        ])
        .select("id, title, description, user_id, slug")
        .single();

      if (error) {
        console.error("Error al crear curso:", error);
        throw new Error(error.message || "Error al crear el curso");
      }

      console.log("Curso creado correctamente:", course);
      return course;
    } catch (error) {
      console.error("Error en addCourse:", error);
      throw error;
    }
  }

  async deleteCourse({ courseId }: DeleteCourseParams): Promise<void> {
    const { error } = await this._fetcher
      .from("courses")
      .delete()
      .eq("id", courseId);

    if (error) {
      throw new Error(error.message);
    }
  }
}
