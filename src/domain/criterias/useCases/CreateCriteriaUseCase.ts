import { HttpRepository } from "../repositories";
import { CriteriasUseCaseDependencies } from "../types";

interface CreateCriteriaInput {
  name: string;
  description: string;
  user_id: string;
  subject_id: string;
}

export class CreateCriteriaUseCase {
  private readonly _repository: HttpRepository;

  constructor({ repository }: CriteriasUseCaseDependencies) {
    this._repository = repository;
  }

  static create(): CreateCriteriaUseCase {
    const repository = HttpRepository.create();
    return new CreateCriteriaUseCase({ repository });
  }

  async execute(input: CreateCriteriaInput) {
    try {
      await this._repository.createCriteria(input);
    } catch (error) {
      console.error("Error en CreateCriteriaUseCase.execute:", error);
      throw new Error(
        `Error al crear el criterio: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
