import { HttpRepository } from "../repositories";
import { CriteriasUseCaseDependencies } from "../types";

interface UpdateCriteriaInput {
  id: string;
  name: string;
  description: string;
  subject_id: string;
}

export class UpdateCriteriaUseCase {
  private _repository: HttpRepository;

  constructor({ repository }: CriteriasUseCaseDependencies) {
    this._repository = repository;
  }

  static create(): UpdateCriteriaUseCase {
    const repository = HttpRepository.create();
    return new UpdateCriteriaUseCase({ repository });
  }

  async execute(input: UpdateCriteriaInput) {
    try {
      return await this._repository.updateCriteria(input);
    } catch (error) {
      console.error("Error en UpdateCriteriaUseCase.execute:", error);
      throw new Error(
        `Error al actualizar el criterio: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
