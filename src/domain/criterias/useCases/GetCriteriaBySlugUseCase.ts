import { HttpRepository } from "../repositories";
import { CriteriasUseCaseDependencies } from "../types";

export class GetCriteriaBySlugUseCase {
  private readonly _repository: HttpRepository;

  constructor({ repository }: CriteriasUseCaseDependencies) {
    this._repository = repository;
  }

  static create(): GetCriteriaBySlugUseCase {
    const repository = HttpRepository.create();
    return new GetCriteriaBySlugUseCase({ repository });
  }

  async execute(slug: string) {
    try {
      // Primero intentamos obtener por slug
      const criteriaBySlug = await this._repository.getCriteriaBySlug(slug);
      if (criteriaBySlug) {
        return criteriaBySlug;
      }

      // Si no se encuentra por slug, intentamos por ID
      const criteriaById = await this._repository.getCriteriaById(slug);
      if (criteriaById) {
        return criteriaById;
      }

      // Si no se encuentra ni por slug ni por ID, retornamos null
      return null;
    } catch (error) {
      console.error("Error in GetCriteriaBySlugUseCase.execute:", error);
      throw new Error(
        `Error al obtener los criterios por slug: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
