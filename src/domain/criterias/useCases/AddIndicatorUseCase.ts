import { supabase } from "@/utils/supabase/client";

export interface AddIndicatorParams {
  title: string;
  criteria_id: string;
}

export interface Indicator {
  id: string;
  title: string;
  criteria_id: string;
  created_at: string;
  updated_at: string;
}

export class AddIndicatorUseCase {
  static async create(): Promise<AddIndicatorUseCase> {
    return new AddIndicatorUseCase();
  }

  async execute(params: AddIndicatorParams): Promise<Indicator> {
    try {
      const { data, error } = await supabase
        .from("indicators")
        .insert([params])
        .select()
        .single();

      if (error) {
        throw new Error(`Error al crear el indicador: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error("Error en AddIndicatorUseCase:", error);
      throw error;
    }
  }
}
