import { HttpRepository } from "../repositories";
import { CriteriasUseCaseDependencies } from "../types";

export class DeleteCriteriaUseCase {
  private readonly _repository: HttpRepository;

  constructor({ repository }: CriteriasUseCaseDependencies) {
    this._repository = repository;
  }

  static create(): DeleteCriteriaUseCase {
    const repository = HttpRepository.create();
    return new DeleteCriteriaUseCase({ repository });
  }

  async execute(criteriaId: string): Promise<void> {
    try {
      await this._repository.deleteCriteria(criteriaId);
    } catch (error) {
      console.error("Error en DeleteCriteriaUseCase.execute:", error);
      throw new Error(
        `Error al eliminar el criterio: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
