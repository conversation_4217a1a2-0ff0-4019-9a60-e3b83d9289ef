import { supabase } from "@/utils/supabase/client";

export interface Indicator {
  id: string;
  title: string;
  criteria_id: string;
  created_at: string;
  updated_at: string;
}

export class GetIndicatorsUseCase {
  static async create(): Promise<GetIndicatorsUseCase> {
    return new GetIndicatorsUseCase();
  }

  async execute(criteriaId: string): Promise<Indicator[]> {
    try {
      console.log("Obteniendo indicadores para el criterio:", criteriaId);
      const { data, error } = await supabase
        .from("indicators")
        .select("*")
        .eq("criteria_id", criteriaId)
        .order("created_at", { ascending: true });

      if (error) {
        console.error("Error al obtener los indicadores:", error);
        throw new Error(`Error al obtener los indicadores: ${error.message}`);
      }

      console.log("Indicadores obtenidos:", data);
      return data || [];
    } catch (error) {
      console.error("Error en GetIndicatorsUseCase:", error);
      throw error;
    }
  }
}
