import { HttpRepository } from "../repositories";
import { Criteria, CriteriasUseCaseDependencies } from "../types";

export class GetCriteriasBySubjectIdUseCase {
  private _repository: HttpRepository;

  constructor({ repository }: CriteriasUseCaseDependencies) {
    this._repository = repository;
  }

  static create(): GetCriteriasBySubjectIdUseCase {
    const repository = HttpRepository.create();
    return new GetCriteriasBySubjectIdUseCase({ repository });
  }

  async execute(subjectId: string): Promise<Criteria[]> {
    try {
      return await this._repository.getCriteriasBySubjectId(subjectId);
    } catch (error) {
      console.error("Error in GetCriteriasBySubjectIdUseCase.execute:", error);
      throw new Error(
        `Error al obtener los criterios de la asignatura: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
