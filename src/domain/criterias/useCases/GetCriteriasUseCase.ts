import { HttpRepository } from "../repositories";
import { CriteriasUseCaseDependencies } from "../types";

export class GetCriteriasUseCase {
  private readonly _repository: HttpRepository;

  constructor({ repository }: CriteriasUseCaseDependencies) {
    this._repository = repository;
  }

  static create(): GetCriteriasUseCase {
    const repository = HttpRepository.create();
    return new GetCriteriasUseCase({ repository });
  }

  async execute(userId: string, search?: string) {
    try {
      return await this._repository.getCriterias(userId, search);
    } catch (error) {
      console.error("Error in GetCriteriasUseCase.execute:", error);
      throw new Error(
        `Error al obtener los criterios: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
