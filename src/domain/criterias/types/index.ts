export interface Criteria {
  id: string;
  name: string;
  description: string;
  subject_id: string;
  slug: string;
  subject?: {
    id: string;
    name: string;
    slug: string;
  };
}

export interface Indicator {
  id: string;
  title: string;
  criteria_id: string;
  is_active: boolean;
  slug: string;
  created_at: string;
  updated_at: string;
}

export interface CriteriasUseCaseDependencies {
  repository: any; // Evitar referencia circular
}
