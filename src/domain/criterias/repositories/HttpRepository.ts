import { supabase } from "@/utils/supabase/client";
import { SupabaseClient } from "@supabase/supabase-js";
import { Criteria } from "../types";
import { slugify } from "@/utils/slugify";
import { Subject } from "@/domain/courses/types";

interface HttpRepositoryDependencies {
  fetcher: SupabaseClient;
}

interface SubjectInfo extends Subject {
  course: {
    id: string;
    title: string;
    description: string;
    user_id: string;
  };
}

export class HttpRepository {
  private readonly _fetcher: SupabaseClient;

  static create(): HttpRepository {
    return new HttpRepository({ fetcher: supabase });
  }

  constructor({ fetcher }: HttpRepositoryDependencies) {
    this._fetcher = fetcher;
  }

  async createCriteria(input: {
    name: string;
    description: string;
    user_id: string;
    subject_id: string;
  }): Promise<void> {
    try {
      await this._fetcher.from("criterias").insert([input]);
    } catch (error) {
      console.error("<PERSON>rror en HttpRepository.createCriteria:", error);
      throw new Error(
        `Error al crear el criterio: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  async getCriteriasBySubjectId(subjectId: string): Promise<Criteria[]> {
    try {
      console.log(`Obteniendo criterios para asignatura: ${subjectId}`);
      const { data, error } = await supabase
        .from("criterias")
        .select(
          `
          id,
          name,
          description,
          slug,
          subject_id,
          created_at,
          updated_at
        `
        )
        .eq("subject_id", subjectId)
        .order("created_at", { ascending: true });

      if (error) {
        console.error("Error fetching subject criterias:", error);
        throw new Error("Error al obtener los criterios de la asignatura");
      }

      console.log("Criterios obtenidos:", data);
      return data as Criteria[];
    } catch (err) {
      console.error("Error in GetCriteriasBySubjectIdUseCase.execute:", err);
      throw new Error(
        `Error al obtener los criterios: ${
          err instanceof Error ? err.message : String(err)
        }`
      );
    }
  }

  async getCriterias(userId?: string, search?: string): Promise<Criteria[]> {
    try {
      let query = this._fetcher
        .from("criterias")
        .select(
          `
          *,
          subject:subject_id (
            id,
            name
          )
        `
        )
        .order("created_at", { ascending: true });

      if (userId) {
        const { data: subjects } = await this._fetcher
          .from("subjects")
          .select("id")
          .eq("user_id", userId);

        if (subjects && subjects.length > 0) {
          const subjectIds = subjects.map((subject) => subject.id);
          query = query.in("subject_id", subjectIds);
        }
      }

      // Add search functionality if search parameter is provided
      if (search && search.trim() !== "") {
        query = query.ilike("name", `%${search}%`);
      }

      const { data: criterias, error } = await query;

      if (error) {
        console.error("Error en getCriterias:", error);
        throw new Error(`Error de base de datos: ${error.message}`);
      }

      return criterias || [];
    } catch (error) {
      console.error("Error en getCriterias:", error);
      throw new Error(
        `Error al obtener los criterios: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  async getCriteriaById(id: string): Promise<Criteria | null> {
    try {
      const { data, error } = await this._fetcher
        .from("criterias")
        .select(
          `
          *,
          subject:subjects (
            id,
            name,
            slug
          )
        `
        )
        .eq("id", id)
        .maybeSingle();

      if (error) {
        console.error("Error en HttpRepository.getCriteriaById:", error);
        throw new Error(`Error al obtener el criterio: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error("Error en HttpRepository.getCriteriaById:", error);
      throw error;
    }
  }

  async getCriteriaBySlug(slug: string): Promise<Criteria | null> {
    try {
      const { data, error } = await this._fetcher
        .from("criterias")
        .select(
          `
          *,
          subject:subjects (
            id,
            name,
            slug
          )
        `
        )
        .eq("slug", slug)
        .maybeSingle();

      if (error) {
        console.error("Error en HttpRepository.getCriteriaBySlug:", error);
        throw new Error(`Error al obtener el criterio: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error("Error en HttpRepository.getCriteriaBySlug:", error);
      throw error;
    }
  }

  async updateCriteria(input: {
    id: string;
    name: string;
    description: string;
    subject_id: string;
  }): Promise<Criteria> {
    try {
      // Primero obtenemos el criterio actual para comparar el nombre
      const { data: currentCriteria, error: fetchError } = await supabase
        .from("criterias")
        .select("name")
        .eq("id", input.id)
        .single();

      if (fetchError) {
        throw new Error(
          `Error al obtener el criterio actual: ${fetchError.message}`
        );
      }

      // Si el nombre ha cambiado, actualizamos también el slug
      const updateData = {
        name: input.name,
        description: input.description,
        subject_id: input.subject_id,
        ...(currentCriteria.name !== input.name && {
          slug: slugify(input.name),
        }),
      };

      const { data, error } = await supabase
        .from("criterias")
        .update(updateData)
        .eq("id", input.id)
        .select()
        .single();

      if (error) {
        throw new Error(`Error al actualizar el criterio: ${error.message}`);
      }

      return data as Criteria;
    } catch (error) {
      console.error("Error en HttpRepository.updateCriteria:", error);
      throw error;
    }
  }

  async deleteCriteria(criteriaId: string): Promise<void> {
    try {
      const { error } = await this._fetcher
        .from("criterias")
        .delete()
        .eq("id", criteriaId);

      if (error) {
        throw new Error(`Error al eliminar el criterio: ${error.message}`);
      }
    } catch (error) {
      console.error("Error en HttpRepository.deleteCriteria:", error);
      throw error;
    }
  }
}
