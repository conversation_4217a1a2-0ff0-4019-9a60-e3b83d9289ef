import { HttpRepository } from "../repositories/HttpRepository";

export interface GetHomeworksListUseCaseDependencies {
  repository: HttpRepository;
}

export interface CreateHomeworkUseCaseDependencies {
  repository: HttpRepository;
}

export interface UpdateHomeworkUseCaseDependencies {
  repository: HttpRepository;
}

export interface AssignCriteriaToHomeworkUseCaseDependencies {
  repository: HttpRepository;
}

export interface Homework {
  id: string;
  title: string;
  description: string;
  subject_id: string;
  criteria_id?: string;
  slug: string;
  created_at: string;
  updated_at: string;
}

export interface HomeworkWithCriteria extends Homework {
  criteria?: {
    id: string;
    name: string;
    description: string;
    slug: string;
  };
}
