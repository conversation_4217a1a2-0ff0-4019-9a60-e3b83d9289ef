import { SupabaseClient } from "@supabase/supabase-js";
import { supabase } from "@/utils/supabase/client";
import { Homework } from "../useCases/CreateHomeworkUseCase";
import { HomeworkWithSubject } from "../useCases/GetHomeworksListUseCase";

interface HttpRepositoryDependencies {
  fetcher: SupabaseClient;
}

export class HttpRepository {
  private _fetcher: SupabaseClient;

  static create(): HttpRepository {
    return new HttpRepository({ fetcher: supabase });
  }

  constructor({ fetcher }: HttpRepositoryDependencies) {
    this._fetcher = fetcher;
  }

  async getHomeworksList(): Promise<HomeworkWithSubject[]> {
    try {
      // Primero obtenemos los trabajos con su información básica
      const { data: homeworks, error } = await this._fetcher
        .from("homeworks")
        .select(
          `
          *,
          subject:subject_id (
            id,
            name,
            slug
          )
        `
        )
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching homeworks:", error);
        throw new Error("Error al cargar la lista de trabajos");
      }

      // Para cada trabajo, obtenemos sus criterios asociados
      const homeworksWithCriterias = await Promise.all(
        homeworks.map(async (homework) => {
          const { data: criterias, error: criteriasError } = await this._fetcher
            .from("homework_criteria")
            .select(
              `
              criteria:criteria_id (
                id,
                name,
                description,
                slug
              )
            `
            )
            .eq("homework_id", homework.id);

          if (criteriasError) {
            console.error(
              "Error fetching criterias for homework:",
              criteriasError
            );
            return {
              ...homework,
              criterias: [],
            };
          }

          return {
            ...homework,
            criterias: criterias.map((item) => item.criteria),
          };
        })
      );

      return homeworksWithCriterias as HomeworkWithSubject[];
    } catch (err) {
      console.error("Error in HttpRepository.getHomeworksList:", err);
      throw new Error("Error al cargar la lista de trabajos");
    }
  }

  async getHomeworksBySubjectId(
    subjectId: string
  ): Promise<HomeworkWithSubject[]> {
    try {
      // Primero obtenemos los trabajos con su información básica
      const { data: homeworks, error } = await this._fetcher
        .from("homeworks")
        .select(
          `
          *,
          subject:subject_id (
            id,
            name,
            slug
          )
        `
        )
        .eq("subject_id", subjectId)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching homeworks by subject:", error);
        throw new Error("Error al cargar los trabajos de la asignatura");
      }

      // Para cada trabajo, obtenemos sus criterios asociados
      const homeworksWithCriterias = await Promise.all(
        homeworks.map(async (homework) => {
          const { data: criterias, error: criteriasError } = await this._fetcher
            .from("homework_criteria")
            .select(
              `
              criteria:criteria_id (
                id,
                name,
                description,
                slug
              )
            `
            )
            .eq("homework_id", homework.id);

          if (criteriasError) {
            console.error(
              "Error fetching criterias for homework:",
              criteriasError
            );
            return {
              ...homework,
              criterias: [],
            };
          }

          return {
            ...homework,
            criterias: criterias.map((item) => item.criteria),
          };
        })
      );

      return homeworksWithCriterias as HomeworkWithSubject[];
    } catch (err) {
      console.error("Error in HttpRepository.getHomeworksBySubjectId:", err);
      throw new Error("Error al cargar los trabajos de la asignatura");
    }
  }

  async createHomework(input: {
    title: string;
    description: string;
    subject_id: string;
    user_id?: string; // Marcado como opcional ya que no existe en la tabla
  }): Promise<Homework> {
    try {
      console.log("Creating homework with input:", input);

      // Generamos el slug a partir del título
      const slug = input.title
        .toLowerCase()
        .replace(/[^\w\s]/gi, "")
        .replace(/\s+/g, "-");

      const { data, error } = await this._fetcher
        .from("homeworks")
        .insert([
          {
            title: input.title,
            description: input.description,
            subject_id: input.subject_id,
            slug: slug,
            // No incluimos user_id ya que no existe en la tabla
          },
        ])
        .select()
        .single();

      if (error) {
        console.error("Error creating homework:", error);
        throw new Error(`Error al crear el trabajo: ${error.message}`);
      }

      if (!data) {
        throw new Error("No se pudo crear el trabajo: no se recibieron datos");
      }

      return data as Homework;
    } catch (err) {
      console.error("Error in HttpRepository.createHomework:", err);
      throw new Error(
        `Error al crear el trabajo: ${
          err instanceof Error ? err.message : String(err)
        }`
      );
    }
  }

  async updateHomework(input: {
    id: string;
    title: string;
    description: string;
    subject_id: string;
  }): Promise<{ id: string; slug: string }> {
    try {
      console.log("Updating homework with input:", input);

      // Generamos el slug a partir del título
      const slug = input.title
        .toLowerCase()
        .replace(/[^\w\s]/gi, "")
        .replace(/\s+/g, "-");

      const { data, error } = await this._fetcher
        .from("homeworks")
        .update({
          title: input.title,
          description: input.description,
          subject_id: input.subject_id,
          slug: slug,
          updated_at: new Date().toISOString(),
        })
        .eq("id", input.id)
        .select()
        .single();

      if (error) {
        console.error("Error updating homework:", error);
        throw new Error(`Error al actualizar el trabajo: ${error.message}`);
      }

      if (!data) {
        throw new Error(
          "No se pudo actualizar el trabajo: no se recibieron datos"
        );
      }

      return { id: data.id, slug: data.slug };
    } catch (err) {
      console.error("Error in HttpRepository.updateHomework:", err);
      throw new Error(
        `Error al actualizar el trabajo: ${
          err instanceof Error ? err.message : String(err)
        }`
      );
    }
  }

  async assignCriteriaToHomework(input: {
    homework_id: string;
    criteria_id: string | null;
  }): Promise<void> {
    try {
      console.log("Assigning criteria to homework:", input);

      // Este método se mantiene por compatibilidad, pero ahora usaremos la tabla de relación
      if (input.criteria_id) {
        // Si hay un criterio, lo añadimos a la tabla de relación
        const { error } = await this._fetcher.from("homework_criteria").insert({
          homework_id: input.homework_id,
          criteria_id: input.criteria_id,
        });

        if (error) {
          console.error("Error assigning criteria to homework:", error);
          throw new Error(
            `Error al asignar criterio al trabajo: ${error.message}`
          );
        }
      } else {
        // Si no hay criterio, eliminamos todas las relaciones
        const { error } = await this._fetcher
          .from("homework_criteria")
          .delete()
          .eq("homework_id", input.homework_id);

        if (error) {
          console.error("Error removing criteria from homework:", error);
          throw new Error(
            `Error al eliminar criterios del trabajo: ${error.message}`
          );
        }
      }
    } catch (err) {
      console.error("Error in HttpRepository.assignCriteriaToHomework:", err);
      throw new Error(
        `Error al asignar criterio al trabajo: ${
          err instanceof Error ? err.message : String(err)
        }`
      );
    }
  }

  async assignCriteriasToHomework(input: {
    homework_id: string;
    criteria_ids: string[];
  }): Promise<void> {
    try {
      console.log("Assigning multiple criterias to homework:", input);

      // Primero eliminamos todas las relaciones existentes
      const { error: deleteError } = await this._fetcher
        .from("homework_criteria")
        .delete()
        .eq("homework_id", input.homework_id);

      if (deleteError) {
        console.error(
          "Error removing existing criteria from homework:",
          deleteError
        );
        throw new Error(
          `Error al eliminar criterios existentes: ${deleteError.message}`
        );
      }

      // Si hay criterios seleccionados, los añadimos
      if (input.criteria_ids.length > 0) {
        // Preparamos los datos para inserción masiva
        const criteriaRelations = input.criteria_ids.map((criteria_id) => ({
          homework_id: input.homework_id,
          criteria_id: criteria_id,
        }));

        const { error: insertError } = await this._fetcher
          .from("homework_criteria")
          .insert(criteriaRelations);

        if (insertError) {
          console.error("Error assigning criterias to homework:", insertError);
          throw new Error(
            `Error al asignar criterios al trabajo: ${insertError.message}`
          );
        }
      }
    } catch (err) {
      console.error("Error in HttpRepository.assignCriteriasToHomework:", err);
      throw new Error(
        `Error al asignar criterios al trabajo: ${
          err instanceof Error ? err.message : String(err)
        }`
      );
    }
  }
}
