import { HttpRepository } from "../repositories/HttpRepository";
import { UpdateHomeworkUseCaseDependencies } from "../types";

export interface UpdateHomeworkInput {
  id: string;
  title: string;
  description: string;
  subject_id: string;
}

export class UpdateHomeworkUseCase {
  private readonly _repository: HttpRepository;

  static create(): UpdateHomeworkUseCase {
    const repository = HttpRepository.create();
    return new UpdateHomeworkUseCase({ repository });
  }

  constructor({ repository }: UpdateHomeworkUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(input: UpdateHomeworkInput): Promise<{ slug: string }> {
    try {
      const updatedHomework = await this._repository.updateHomework({
        id: input.id,
        title: input.title,
        description: input.description,
        subject_id: input.subject_id,
      });

      return { slug: updatedHomework.slug };
    } catch (err) {
      console.error("Error in UpdateHomeworkUseCase:", err);
      throw new Error("Error al actualizar el trabajo");
    }
  }
}
