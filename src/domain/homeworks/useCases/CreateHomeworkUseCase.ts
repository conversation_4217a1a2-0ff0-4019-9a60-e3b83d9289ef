import { HttpRepository } from "../repositories/HttpRepository";
import { CreateHomeworkUseCaseDependencies } from "../types";

export interface Homework {
  id: string;
  title: string;
  description: string;
  subject_id: string;
  created_at: string;
  updated_at: string;
  slug: string;
}

export interface CreateHomeworkInput {
  title: string;
  description: string;
  subject_id: string;
  user_id?: string; // Marcado como opcional ya que no existe en la tabla
}

export class CreateHomeworkUseCase {
  private readonly _repository: HttpRepository;

  static create(): CreateHomeworkUseCase {
    const repository = HttpRepository.create();
    return new CreateHomeworkUseCase({ repository });
  }

  constructor({ repository }: CreateHomeworkUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(input: CreateHomeworkInput): Promise<Homework> {
    try {
      const homework = await this._repository.createHomework({
        title: input.title,
        description: input.description,
        subject_id: input.subject_id,
        // No pasamos user_id ya que no existe en la tabla
      });

      return homework;
    } catch (err) {
      console.error("Error in CreateHomeworkUseCase:", err);
      throw new Error("Error al crear el trabajo");
    }
  }
}
