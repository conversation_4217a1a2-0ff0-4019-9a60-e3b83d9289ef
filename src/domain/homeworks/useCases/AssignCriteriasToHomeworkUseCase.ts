import { HttpRepository } from "../repositories/HttpRepository";
import { AssignCriteriaToHomeworkUseCaseDependencies } from "../types";

export interface AssignCriteriasToHomeworkInput {
  homework_id: string;
  criteria_ids: string[]; // Array de IDs de criterios
}

export class AssignCriteriasToHomeworkUseCase {
  private _repository: HttpRepository;

  static create(): AssignCriteriasToHomeworkUseCase {
    const repository = HttpRepository.create();
    return new AssignCriteriasToHomeworkUseCase({ repository });
  }

  constructor({ repository }: AssignCriteriaToHomeworkUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(input: AssignCriteriasToHomeworkInput): Promise<void> {
    try {
      await this._repository.assignCriteriasToHomework({
        homework_id: input.homework_id,
        criteria_ids: input.criteria_ids,
      });
    } catch (err) {
      console.error("Error in AssignCriteriasToHomeworkUseCase:", err);
      throw new Error("Error al asignar criterios al trabajo");
    }
  }
}
