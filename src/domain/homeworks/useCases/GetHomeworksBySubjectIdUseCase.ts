import { HttpRepository } from "../repositories/HttpRepository";
import { GetHomeworksListUseCaseDependencies } from "../types";
import { HomeworkWithSubject } from "./GetHomeworksListUseCase";

export class GetHomeworksBySubjectIdUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetHomeworksBySubjectIdUseCase {
    const repository = HttpRepository.create();
    return new GetHomeworksBySubjectIdUseCase({ repository });
  }

  constructor({ repository }: GetHomeworksListUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(subjectId: string): Promise<HomeworkWithSubject[]> {
    try {
      const homeworks = await this._repository.getHomeworksBySubjectId(
        subjectId
      );
      return homeworks;
    } catch (err) {
      console.error("Error in GetHomeworksBySubjectIdUseCase:", err);
      throw new Error("<PERSON>rror al cargar los trabajos de la asignatura");
    }
  }
}
