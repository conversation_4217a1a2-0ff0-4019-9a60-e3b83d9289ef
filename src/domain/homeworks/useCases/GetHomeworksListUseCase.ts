import { Homework } from "./CreateHomeworkUseCase";
import { HttpRepository } from "../repositories/HttpRepository";
import { GetHomeworksListUseCaseDependencies } from "../types";

export interface HomeworkWithSubject extends Homework {
  subject: {
    id: string;
    name: string;
    slug: string;
  };
}

export class GetHomeworksListUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetHomeworksListUseCase {
    const repository = HttpRepository.create();
    return new GetHomeworksListUseCase({ repository });
  }

  constructor({ repository }: GetHomeworksListUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(): Promise<HomeworkWithSubject[]> {
    try {
      const homeworks = await this._repository.getHomeworksList();
      return homeworks;
    } catch (err) {
      console.error("Error in GetHomeworksListUseCase:", err);
      throw new Error("Error al cargar la lista de trabajos");
    }
  }
}
