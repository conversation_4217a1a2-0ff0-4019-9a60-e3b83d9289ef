import { HttpRepository } from "../repositories/HttpRepository";
import { AssignCriteriaToHomeworkUseCaseDependencies } from "../types";

export interface AssignCriteriaToHomeworkInput {
  homework_id: string;
  criteria_id: string | null; // null para desasignar
}

export class AssignCriteriaToHomeworkUseCase {
  private _repository: HttpRepository;

  static create(): AssignCriteriaToHomeworkUseCase {
    const repository = HttpRepository.create();
    return new AssignCriteriaToHomeworkUseCase({ repository });
  }

  constructor({ repository }: AssignCriteriaToHomeworkUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(input: AssignCriteriaToHomeworkInput): Promise<void> {
    try {
      await this._repository.assignCriteriaToHomework({
        homework_id: input.homework_id,
        criteria_id: input.criteria_id,
      });
    } catch (err) {
      console.error("Error in AssignCriteriaToHomeworkUseCase:", err);
      throw new Error("Error al asignar criterio al trabajo");
    }
  }
}
