import { HttpRepository } from "../repositories";
import {
  Subject,
  UpdateSubjectParams,
  GetSubjectsUseCaseDependencies,
} from "../types";

export class UpdateSubjectUseCase {
  private readonly _repository: HttpRepository;

  static create(): UpdateSubjectUseCase {
    const repository = HttpRepository.create();
    return new UpdateSubjectUseCase({ repository });
  }
  constructor({ repository }: GetSubjectsUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(params: UpdateSubjectParams): Promise<Subject> {
    return this._repository.updateSubject(params);
  }
}
