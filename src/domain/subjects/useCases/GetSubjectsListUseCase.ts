import { HttpRepository } from "../repositories";
import { Subject } from "../types";

interface GetSubjectsListUseCaseDependencies {
  repository: HttpRepository;
}

export class GetSubjectsListUseCase {
  private _repository: HttpRepository;

  static create(): GetSubjectsListUseCase {
    const repository = HttpRepository.create();
    return new GetSubjectsListUseCase({ repository });
  }

  constructor({ repository }: GetSubjectsListUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(): Promise<Subject[]> {
    try {
      const subjects = await this._repository.getSubjectsList();
      console.log("Asignaturas obtenidas:", subjects);
      return subjects;
    } catch (error) {
      console.error("Error en GetSubjectsListUseCase.execute:", error);
      throw new Error(
        `Error al cargar las asignaturas: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
