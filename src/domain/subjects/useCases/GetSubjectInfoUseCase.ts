import { HttpRepository } from "../repositories";

// Importamos los tipos necesarios del repositorio
import { Subject } from "../types";

// Definimos el tipo SubjectInfo basado en el tipo Subject
interface SubjectInfo extends Subject {
  course: {
    id: string;
    title: string;
    description: string;
    user_id: string;
    slug?: string;
  };
}

interface GetSubjectInfoUseCaseDependencies {
  repository: HttpRepository;
}

interface GetSubjectInfoParams {
  subjectId: string;
}

export class GetSubjectInfoUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetSubjectInfoUseCase {
    const repository = HttpRepository.create();
    return new GetSubjectInfoUseCase({ repository });
  }

  constructor({ repository }: GetSubjectInfoUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({
    subjectId,
  }: GetSubjectInfoParams): Promise<SubjectInfo | undefined> {
    return await this._repository.getSubjectInfo(subjectId);
  }
}
