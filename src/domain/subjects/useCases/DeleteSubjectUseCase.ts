import { HttpRepository } from "../repositories";
import { GetSubjectsUseCaseDependencies } from "../types";
export class DeleteSubjectUseCase {
  private _repository: HttpRepository;

  constructor({ repository }: GetSubjectsUseCaseDependencies) {
    this._repository = repository;
  }

  static create(): DeleteSubjectUseCase {
    const repository = HttpRepository.create();
    return new DeleteSubjectUseCase({ repository });
  }

  async execute(subjectId: string): Promise<void> {
    return this._repository.deleteSubject({ subjectId });
  }
}
