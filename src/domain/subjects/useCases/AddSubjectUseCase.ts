import { HttpRepository } from "../repositories";

interface AddSubjectUseCaseDependencies {
  repository: HttpRepository;
}

interface AddSubjectParams {
  name: string;
  courseId: string;
}

export class AddSubjectUseCase {
  private _repository: HttpRepository;

  static create(): AddSubjectUseCase {
    const repository = HttpRepository.create();
    return new AddSubjectUseCase({ repository });
  }

  constructor({ repository }: AddSubjectUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({ name, courseId }: AddSubjectParams): Promise<void> {
    await this._repository.addSubject({ name, courseID: courseId });
  }
}
