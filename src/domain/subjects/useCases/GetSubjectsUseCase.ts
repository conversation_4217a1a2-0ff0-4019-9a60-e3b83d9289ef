import { HttpRepository } from "../repositories/HttpRepository";
import { Subject, GetSubjectsUseCaseDependencies } from "../types";

export class GetSubjectsUseCase {
  private _repository: HttpRepository;

  constructor({ repository }: GetSubjectsUseCaseDependencies) {
    this._repository = repository;
  }

  static create(): GetSubjectsUseCase {
    const repository = HttpRepository.create();
    return new GetSubjectsUseCase({ repository });
  }

  async execute(userId: string): Promise<Subject[]> {
    try {
      const subjects = await this._repository.getSubjects(userId);
      console.log("Asignaturas obtenidas:", subjects);
      return subjects;
    } catch (error) {
      console.error("Error en GetSubjectsUseCase.execute:", error);
      throw new Error(
        `Error al cargar las asignaturas: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
