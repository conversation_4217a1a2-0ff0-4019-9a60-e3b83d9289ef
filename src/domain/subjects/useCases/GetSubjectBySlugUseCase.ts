import { HttpRepository } from "../repositories/HttpRepository";
import { Subject, GetSubjectsUseCaseDependencies } from "../types";

export class GetSubjectBySlugUseCase {
  private _repository: HttpRepository;

  constructor({ repository }: GetSubjectsUseCaseDependencies) {
    this._repository = repository;
  }

  static create(): GetSubjectBySlugUseCase {
    const repository = HttpRepository.create();
    return new GetSubjectBySlugUseCase({ repository });
  }

  async execute(slug: string): Promise<Subject | null> {
    try {
      console.log(`Obteniendo asignatura con slug: ${slug}`);
      const subject = await this._repository.getSubjectBySlug(slug);
      console.log("Asignatura obtenida:", subject);
      return subject;
    } catch (error) {
      console.error("Error en GetSubjectBySlugUseCase.execute:", error);
      throw new Error(
        `Error al cargar la asignatura: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
