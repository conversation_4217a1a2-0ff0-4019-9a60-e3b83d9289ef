import { HttpRepository } from "../repositories";
import { GetSubjectsUseCaseDependencies } from "../types";

export interface SubjectWithCriterias {
  subject: {
    id: string;
    name: string;
    slug: string;
    course: {
      id: string;
      title: string;
      description: string;
      user_id: string;
    };
  };
  criterias: Array<{
    id: string;
    name: string;
    description: string;
    slug: string;
    indicators: Array<{
      id: string;
      title: string;
      slug: string;
      levels: Array<{
        id: string;
        level_number: number;
        title: string;
      }>;
    }>;
  }>;
}

export class GetSubjectWithCriteriasUseCase {
  private _repository: HttpRepository;

  constructor({ repository }: GetSubjectsUseCaseDependencies) {
    this._repository = repository;
  }

  static create() {
    const repository = HttpRepository.create();
    return new GetSubjectWithCriteriasUseCase({ repository });
  }

  async execute(subjectId: string): Promise<SubjectWithCriterias | null> {
    try {
      return await this._repository.getSubjectWithCriteriasAndIndicators(
        subjectId
      );
    } catch (error) {
      console.error("Error en GetSubjectWithCriteriasUseCase.execute:", error);
      throw new Error(
        `Error al obtener la asignatura con sus criterios: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
