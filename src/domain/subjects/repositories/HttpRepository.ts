import { supabase } from "@/utils/supabase/client";
import { SupabaseClient } from "@supabase/supabase-js";
import { slugify } from "@/utils/slugify";
import {
  Subject,
  AddSubjectParams,
  DeleteSubjectParams,
  UpdateSubjectParams,
} from "../types";

interface HttpRepositoryDependencies {
  fetcher: SupabaseClient;
}

interface SubjectInfo extends Subject {
  course: {
    id: string;
    title: string;
    description: string;
    user_id: string;
    slug: string;
  };
}

export class HttpRepository {
  private readonly _fetcher: SupabaseClient;

  static create(): HttpRepository {
    return new HttpRepository({ fetcher: supabase });
  }

  constructor({ fetcher }: HttpRepositoryDependencies) {
    this._fetcher = fetcher;
  }

  async getSubjectsList(userId?: string): Promise<Subject[]> {
    try {
      let query = this._fetcher
        .from("subjects")
        .select(
          `
          *,
          course:course_id (
            id,
            title,
            user_id
          )
        `
        )
        .order("name");

      if (userId) {
        const { data: courses } = await this._fetcher
          .from("courses")
          .select("id")
          .eq("user_id", userId);

        if (courses && courses.length > 0) {
          const courseIds = courses.map((course) => course.id);
          query = query.in("course_id", courseIds);
        }
      }

      const { data: subjects, error } = await query;

      if (error) {
        console.error("Error en getSubjectsList:", error);
        throw new Error(`Error de base de datos: ${error.message}`);
      }

      return subjects || [];
    } catch (error) {
      console.error("Error en getSubjectsList:", error);
      throw new Error(
        `Error al obtener las asignaturas: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  async addSubject({ name, courseID }: AddSubjectParams): Promise<Subject> {
    // Primero obtenemos el curso para obtener el user_id
    const { data: course, error: courseError } = await this._fetcher
      .from("courses")
      .select("user_id")
      .eq("id", courseID)
      .single();

    if (courseError || !course) {
      throw new Error("No se pudo encontrar el curso");
    }

    const slug = slugify(name);

    // Ahora insertamos la materia con el user_id del curso y el slug
    const { data: subject, error } = await this._fetcher
      .from("subjects")
      .insert([{ name, course_id: courseID, user_id: course.user_id, slug }])
      .select("*")
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return subject;
  }

  async deleteSubject({ subjectId }: DeleteSubjectParams): Promise<void> {
    const { error } = await this._fetcher
      .from("subjects")
      .delete()
      .eq("id", subjectId);

    if (error) {
      throw new Error(error.message);
    }
  }

  async getSubjectInfo(subjectId: string): Promise<SubjectInfo | undefined> {
    const { data: subject, error } = await this._fetcher
      .from("subjects")
      .select(
        `
        *,
        course:course_id (
          id,
          title,
          description,
          user_id
        )
      `
      )
      .eq("id", subjectId)
      .single();

    if (error) {
      console.error(error);
      return undefined;
    }

    return subject;
  }

  async getSubjectBySlug(slug: string): Promise<Subject | null> {
    try {
      const { data, error } = await this._fetcher
        .from("subjects")
        .select(
          `
          *,
          course:course_id (
            id,
            title,
            description,
            user_id,
            slug
          )
        `
        )
        .eq("slug", slug)
        .maybeSingle();

      if (error) {
        console.error("Error en HttpRepository.getSubjectBySlug:", error);
        throw new Error(`Error al obtener la asignatura: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error("Error en HttpRepository.getSubjectBySlug:", error);
      throw error;
    }
  }

  async updateSubject(params: UpdateSubjectParams): Promise<Subject> {
    try {
      const { id, course_id, ...updateData } = params;

      // Si se actualiza el nombre, actualizar también el slug
      if (updateData.name && !updateData.slug) {
        updateData.slug = slugify(updateData.name);
      }

      // Preparar los datos para la actualización
      const dataToUpdate: any = { ...updateData };

      // Si se proporciona un course_id, actualizarlo en la base de datos
      if (course_id) {
        dataToUpdate.course_id = course_id;
      }

      const { data, error } = await this._fetcher
        .from("subjects")
        .update(dataToUpdate)
        .eq("id", id)
        .select(
          `
          *,
          course:course_id (
            id,
            title,
            description,
            user_id,
            slug
          )
        `
        )
        .single();

      if (error) {
        throw new Error(`Error al actualizar la asignatura: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error("Error en updateSubject:", error);
      throw error;
    }
  }

  async getSubjects(userId?: string): Promise<Subject[]> {
    return this.getSubjectsList(userId);
  }

  async getSubjectWithCriteriasAndIndicators(subjectId: string): Promise<{
    subject: SubjectInfo;
    criterias: Array<{
      id: string;
      name: string;
      description: string;
      slug: string;
      indicators: Array<{
        id: string;
        title: string;
        slug: string;
        levels: Array<{
          id: string;
          level_number: number;
          title: string;
        }>;
      }>;
    }>;
  } | null> {
    try {
      // Obtener la asignatura con su curso
      const { data: subject, error: subjectError } = await this._fetcher
        .from("subjects")
        .select(
          `
          *,
          course:course_id (
            id,
            title,
            description,
            user_id,
            slug
          )
        `
        )
        .eq("id", subjectId)
        .single();

      if (subjectError || !subject) {
        throw new Error("No se pudo encontrar la asignatura");
      }

      // Obtener los criterios de la asignatura
      const { data: criterias, error: criteriasError } = await this._fetcher
        .from("criterias")
        .select(
          `
          id,
          name,
          description,
          slug,
          indicators (
            id,
            title,
            slug,
            levels:indicator_levels (
              id,
              level_number,
              title
            )
          )
        `
        )
        .eq("subject_id", subjectId)
        .order("created_at", { ascending: true });

      if (criteriasError) {
        throw new Error(
          `Error al obtener los criterios: ${criteriasError.message}`
        );
      }

      return {
        subject,
        criterias: criterias || [],
      };
    } catch (error) {
      console.error("Error en getSubjectWithCriteriasAndIndicators:", error);
      throw new Error(
        `Error al obtener la asignatura con sus criterios: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}
