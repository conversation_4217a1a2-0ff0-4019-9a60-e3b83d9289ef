import { HttpRepository } from "../repositories";

export interface DeleteSubjectUseCaseDependencies {
  repository: HttpRepository;
}

export interface GetSubjectsUseCaseDependencies {
  repository: HttpRepository;
}

export interface UpdateSubjectUseCaseDependencies {
  repository: HttpRepository;
}

export interface Subject {
  id: string;
  name: string;
  description: string;
  slug: string;
  user_id: string;
  created_at?: string;
  updated_at?: string;
}

export interface AddSubjectParams {
  name: string;
  description: string;
  slug: string;
  user_id: string;
}

export interface UpdateSubjectParams {
  id: string;
  name?: string;
  description?: string;
  slug?: string;
  courseId?: string;
}
