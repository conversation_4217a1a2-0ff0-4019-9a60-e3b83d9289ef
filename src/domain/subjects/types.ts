export interface Subject {
  id: string;
  name: string;
  description: string;
  course_id?: string;
  user_id: string;
  created_at?: string;
  updated_at?: string;
  slug: string;
  course?: {
    id: string;
    title: string;
    description?: string;
    user_id: string;
  };
}

export interface AddSubjectParams {
  name: string;
  courseID: string;
}

export interface DeleteSubjectParams {
  subjectId: string;
}

export interface UpdateSubjectParams {
  id: string;
  name?: string;
  slug?: string;
  course_id?: string;
}

export interface GetSubjectsUseCaseDependencies {
  repository: any; // Evitar referencia circular
}
