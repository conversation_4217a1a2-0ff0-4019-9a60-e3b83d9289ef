import { SupabaseClient } from "@supabase/supabase-js";
import { HttpRepository } from "../repositories";

export interface EvaluationsUseCaseDependencies {
  repository: HttpRepository;
}

export interface HttpRepositoryDependencies {
  fetcher: SupabaseClient;
}

export interface Evaluation {
  id: string;
  homework_id: string;
  student_id: string;
  indicator_id: string;
  level_id: string;
  level_number: number;
  level_description: string;
  course_id: string;
  subject_id: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  level?: {
    id: string;
    level_number: number;
    title: string;
  };
  indicator?: {
    id: string;
    title: string;
    slug: string;
    criteria?: {
      id: string;
      name: string;
      slug: string;
    };
  };
  // Datos derivados
  homework?: {
    id: string;
    title: string;
    slug: string;
    subject_id: string;
  };
}

export interface HomeworkForEvaluation {
  id: string;
  title: string;
  description: string;
  slug: string;
  subject_id: string;
  created_at: string;
  updated_at: string;
  subject?: {
    id: string;
    name: string;
    slug: string;
  };
  course?: {
    id: string;
    title: string;
    slug: string;
  };
  criterias?: {
    id: string;
    name: string;
    slug: string;
    indicators?: {
      id: string;
      title: string;
      slug: string;
      levels?: {
        id: string;
        level_number: number;
        title: string;
      }[];
    }[];
  }[];
  evaluatedStudentsCount?: number;
  totalStudentsCount?: number;
}

export interface StudentForEvaluation {
  id: string;
  first_name: string;
  last_name1: string;
  last_name2?: string;
  is_pi: boolean;
  slug: string;
  created_at: string;
  updated_at: string;
  evaluations?: Evaluation[];
  isEvaluated?: boolean;
}

export interface SaveEvaluationParams {
  homework_id: string;
  student_id: string;
  indicator_id: string;
  level_id: string;
}

export interface GetHomeworksParams {
  courseId?: string;
  subjectId?: string;
  search?: string;
}

export interface GetStudentsByHomeworkParams {
  homeworkSlug: string;
}

export interface GetIndicatorsByHomeworkParams {
  homeworkId: string;
}

export interface GetEvaluationsByHomeworkAndStudentParams {
  homeworkId: string;
  studentId: string;
}

export interface GetHomeworksAndEvaluationsByStudentAndSubjectParams {
  student_id: string;
  subject_id: string;
}

export interface GetAllEvaluationsByStudentParams {
  studentId: string;
}

export interface CriteriaEvaluationSummary {
  id: string;
  student_id: string;
  criteria_id: string;
  average_level: number;
  level_description: string;
  created_at: string;
  updated_at: string;
  criteria?: {
    id: string;
    name: string;
    slug: string;
  };
}

export interface GetCriteriaEvaluationSummariesParams {
  studentId: string;
}
