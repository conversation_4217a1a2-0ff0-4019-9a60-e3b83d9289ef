import { HttpRepository } from "../repositories";
import { Evaluation, EvaluationsUseCaseDependencies, SaveEvaluationParams } from "../types";

export class SaveEvaluationUseCase {
  private readonly _repository: HttpRepository;

  static create(): SaveEvaluationUseCase {
    const repository = HttpRepository.create();
    return new SaveEvaluationUseCase({ repository });
  }

  constructor({ repository }: EvaluationsUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(evaluation: SaveEvaluationParams): Promise<Evaluation> {
    try {
      return await this._repository.saveEvaluation(evaluation);
    } catch (err) {
      console.error("Error in SaveEvaluationUseCase:", err);
      throw new Error("Error al guardar la evaluación");
    }
  }
}
