import { HttpRepository } from "../repositories";
import { EvaluationsUseCaseDependencies, GetHomeworksParams, HomeworkForEvaluation } from "../types";

export class GetHomeworksForEvaluationUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetHomeworksForEvaluationUseCase {
    const repository = HttpRepository.create();
    return new GetHomeworksForEvaluationUseCase({ repository });
  }

  constructor({ repository }: EvaluationsUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(params: GetHomeworksParams = {}): Promise<HomeworkForEvaluation[]> {
    try {
      return await this._repository.getHomeworksForEvaluation(params);
    } catch (err) {
      console.error("Error in GetHomeworksForEvaluationUseCase:", err);
      throw new Error("Error al cargar la lista de trabajos para evaluación");
    }
  }
}
