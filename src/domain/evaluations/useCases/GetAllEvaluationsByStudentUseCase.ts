import { HttpRepository } from "../repositories";
import {
  Evaluation,
  EvaluationsUseCaseDependencies,
  GetAllEvaluationsByStudentParams,
} from "../types";

export class GetAllEvaluationsByStudentUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetAllEvaluationsByStudentUseCase {
    const repository = HttpRepository.create();
    return new GetAllEvaluationsByStudentUseCase({ repository });
  }

  constructor({ repository }: EvaluationsUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({
    studentId,
  }: GetAllEvaluationsByStudentParams): Promise<Evaluation[]> {
    try {
      return await this._repository.getAllEvaluationsByStudent({
        studentId,
      });
    } catch (err) {
      console.error("Error in GetAllEvaluationsByStudentUseCase:", err);
      throw new Error(
        "Error al cargar todas las evaluaciones del estudiante"
      );
    }
  }
}
