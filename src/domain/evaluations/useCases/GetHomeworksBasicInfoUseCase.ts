import { HttpRepository } from "../repositories";
import { EvaluationsUseCaseDependencies, GetHomeworksParams, HomeworkForEvaluation } from "../types";

export class GetHomeworksBasicInfoUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetHomeworksBasicInfoUseCase {
    const repository = HttpRepository.create();
    return new GetHomeworksBasicInfoUseCase({ repository });
  }

  constructor({ repository }: EvaluationsUseCaseDependencies) {
    this._repository = repository;
  }

  async execute(params: GetHomeworksParams = {}): Promise<HomeworkForEvaluation[]> {
    try {
      return await this._repository.getHomeworksBasicInfo(params);
    } catch (err) {
      console.error("Error in GetHomeworksBasicInfoUseCase:", err);
      throw new Error("Error al cargar la lista de trabajos");
    }
  }
}
