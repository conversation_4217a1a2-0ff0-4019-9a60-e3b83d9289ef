import { HttpRepository } from "../repositories";
import { EvaluationsUseCaseDependencies, GetStudentsByHomeworkParams, StudentForEvaluation } from "../types";

export class GetStudentsByHomeworkUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetStudentsByHomeworkUseCase {
    const repository = HttpRepository.create();
    return new GetStudentsByHomeworkUseCase({ repository });
  }

  constructor({ repository }: EvaluationsUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({ homeworkSlug }: GetStudentsByHomeworkParams): Promise<StudentForEvaluation[]> {
    try {
      return await this._repository.getStudentsByHomework({ homeworkSlug });
    } catch (err) {
      console.error("Error in GetStudentsByHomeworkUseCase:", err);
      throw new Error("Error al cargar los estudiantes para el trabajo");
    }
  }
}
