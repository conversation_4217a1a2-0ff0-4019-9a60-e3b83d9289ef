import { HttpRepository } from "../repositories";
import {
  EvaluationsUseCaseDependencies,
  GetHomeworksAndEvaluationsByStudentAndSubjectParams,
} from "../types";

export class GetHomeworksAndEvaluationsByStudentAndSubjectUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetHomeworksAndEvaluationsByStudentAndSubjectUseCase {
    const repository = HttpRepository.create();
    return new GetHomeworksAndEvaluationsByStudentAndSubjectUseCase({
      repository,
    });
  }

  constructor({ repository }: EvaluationsUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({
    student_id,
    subject_id,
  }: GetHomeworksAndEvaluationsByStudentAndSubjectParams) {
    try {
      return await this._repository.getHomeworksAndEvaluationsByStudentAndSubject(
        {
          student_id,
          subject_id,
        }
      );
    } catch (err) {
      console.error(
        "Error in GetHomeworksAndEvaluationsByStudentAndSubjectUseCase:",
        err
      );
      throw new Error(
        "Error al cargar los trabajos y evaluaciones del estudiante para la asignatura"
      );
    }
  }
}
