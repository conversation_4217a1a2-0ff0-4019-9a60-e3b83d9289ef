import { HttpRepository } from "../repositories";
import {
  Evaluation,
  EvaluationsUseCaseDependencies,
  GetEvaluationsByHomeworkAndStudentParams,
} from "../types";

export class GetEvaluationsByHomeworkAndStudentUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetEvaluationsByHomeworkAndStudentUseCase {
    const repository = HttpRepository.create();
    return new GetEvaluationsByHomeworkAndStudentUseCase({ repository });
  }

  constructor({ repository }: EvaluationsUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({
    homeworkId,
    studentId,
  }: GetEvaluationsByHomeworkAndStudentParams): Promise<Evaluation[]> {
    try {
      return await this._repository.getEvaluationsByHomeworkAndStudent({
        homeworkId,
        studentId,
      });
    } catch (err) {
      console.error("Error in GetEvaluationsByHomeworkAndStudentUseCase:", err);
      throw new Error(
        "Error al cargar las evaluaciones del estudiante para el trabajo"
      );
    }
  }
}
