import { HttpRepository } from "../repositories";
import { EvaluationsUseCaseDependencies, GetIndicatorsByHomeworkParams } from "../types";

export class GetIndicatorsByHomeworkUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetIndicatorsByHomeworkUseCase {
    const repository = HttpRepository.create();
    return new GetIndicatorsByHomeworkUseCase({ repository });
  }

  constructor({ repository }: EvaluationsUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({ homeworkId }: GetIndicatorsByHomeworkParams) {
    try {
      return await this._repository.getIndicatorsByHomework({ homeworkId });
    } catch (err) {
      console.error("Error in GetIndicatorsByHomeworkUseCase:", err);
      throw new Error("Error al cargar los indicadores para el trabajo");
    }
  }
}
