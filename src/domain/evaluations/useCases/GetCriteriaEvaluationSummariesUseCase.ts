import { HttpRepository } from "../repositories";
import { EvaluationsUseCaseDependencies } from "../types";

interface CriteriaEvaluationSummary {
  id: string;
  student_id: string;
  criteria_id: string;
  average_level: number;
  level_description: string;
  created_at: string;
  updated_at: string;
  criteria?: {
    id: string;
    name: string;
    slug: string;
  };
}

interface GetCriteriaEvaluationSummariesParams {
  studentId: string;
}

export class GetCriteriaEvaluationSummariesUseCase {
  private readonly _repository: HttpRepository;

  static create(): GetCriteriaEvaluationSummariesUseCase {
    const repository = HttpRepository.create();
    return new GetCriteriaEvaluationSummariesUseCase({ repository });
  }

  constructor({ repository }: EvaluationsUseCaseDependencies) {
    this._repository = repository;
  }

  async execute({ studentId }: GetCriteriaEvaluationSummariesParams): Promise<CriteriaEvaluationSummary[]> {
    try {
      return await this._repository.getCriteriaEvaluationSummaries({ studentId });
    } catch (err) {
      console.error("Error in GetCriteriaEvaluationSummariesUseCase:", err);
      throw new Error("Error al cargar los resúmenes de evaluación por criterio");
    }
  }
}
