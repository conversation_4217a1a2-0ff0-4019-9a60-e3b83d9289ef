import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON>ctions,
  Button,
} from "@mui/material";

interface ErrorModalProps {
  open: boolean;
  message: string;
  onClose: () => void;
}

export function ErrorModal({
  open,
  message,
  onClose,
}: Readonly<ErrorModalProps>) {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Error</DialogTitle>
      <DialogContent>
        <p>{message}</p>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Aceptar
        </Button>
      </DialogActions>
    </Dialog>
  );
}
