"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/utils/supabase/client";

export const AuthHandler = () => {
  const router = useRouter();

  useEffect(() => {
    // Verificar si hay un hash en la URL (indicativo de un inicio de sesión exitoso)
    if (window.location.hash && window.location.hash.includes("access_token")) {
      console.log("AuthHandler - Hash detectado en la URL");

      // Procesar el hash para obtener la sesión
      const handleHashChange = async () => {
        try {
          // Supabase detectará automáticamente el hash y establecerá la sesión
          const { data, error } = await supabase.auth.getSession();

          if (error) {
            console.error("AuthHandler - Error al obtener la sesión:", error);
            return;
          }

          if (data.session) {
            console.log("AuthHandler - Sesión establecida correctamente");
            
            // Recuperar el redirectTo de localStorage si existe
            const redirectTo = localStorage.getItem("redirectTo");
            console.log("AuthHandler - redirectTo:", redirectTo);
            
            // Limpiar el localStorage
            localStorage.removeItem("redirectTo");
            
            // Limpiar el hash de la URL
            window.history.replaceState(null, "", window.location.pathname);
            
            // Redirigir a la página solicitada o a la página principal
            if (redirectTo) {
              console.log(`AuthHandler - Redirigiendo a: ${redirectTo}`);
              router.push(redirectTo);
            }
          }
        } catch (error) {
          console.error("AuthHandler - Error inesperado:", error);
        }
      };

      handleHashChange();
    }
  }, [router]);

  // Este componente no renderiza nada visible
  return null;
};
