"use client";

import { Button } from "@mui/material";
import { useRouter } from "next/navigation";
import styles from "./SubjectsList.module.css";

interface Subject {
  id: string;
  name: string;
  course_id: string;
  slug: string;
  course: {
    title: string;
  };
}

interface SubjectsListProps {
  subjects: Subject[];
  onDeleteSubject: (subjectId: string) => void;
}

export const SubjectsList = ({
  subjects,
  onDeleteSubject,
}: SubjectsListProps) => {
  const router = useRouter();

  const handleAccessSubject = (subject: Subject) => {
    if (!subject.slug) {
      console.error("La asignatura no tiene slug:", subject);
      return;
    }
    router.push(`/subjects/${subject.slug}`);
  };

  return (
    <div className={styles.gridContainer}>
      {subjects.map((subject) => (
        <div key={subject.id} className={styles.gridItem}>
          <div className={styles.listContainer}>
            <div>
              <h2>{subject.name}</h2>
              <p>Curso: {subject.course.title}</p>
            </div>
            <div className={styles.ctaContainer}>
              <Button
                variant="contained"
                color="primary"
                onClick={() => handleAccessSubject(subject)}
              >
                Acceder
              </Button>
              <Button
                variant="outlined"
                color="error"
                onClick={() => onDeleteSubject(subject.id)}
              >
                Eliminar
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
