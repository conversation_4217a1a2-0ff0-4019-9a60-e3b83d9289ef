"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogActions,
  <PERSON>alogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Alert,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useRouter } from "next/navigation";
import { useState } from "react";
import styles from "@/app/courses/courses.module.css";

interface Course {
  id: string;
  title: string;
  description: string;
  slug: string;
}

interface CoursesListProps {
  courses: Course[];
  onDeleteCourse: (courseId: string) => Promise<void>;
}

export function CoursesList({ courses, onDeleteCourse }: CoursesListProps) {
  const router = useRouter();
  const [openDialog, setOpenDialog] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState<Course | null>(null);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: "success" | "error" | "info";
  }>({
    open: false,
    message: "",
    severity: "info",
  });

  console.log("Cursos recibidos en CoursesList:", courses);

  const handleAccessCourse = (course: Course) => {
    console.log("Accediendo al curso:", course);
    console.log("Slug del curso:", course.slug);
    if (!course.slug) {
      console.error("El curso no tiene slug:", course);
      return;
    }
    router.push(`/courses/${course.slug}`);
    setSnackbar({
      open: true,
      message: `Accediendo al curso: ${course.title}`,
      severity: "info",
    });
  };

  const handleDeleteClick = (course: Course) => {
    setCourseToDelete(course);
    setOpenDialog(true);
  };

  const handleConfirmDelete = () => {
    if (courseToDelete) {
      onDeleteCourse(courseToDelete.id);
      setOpenDialog(false);
      setCourseToDelete(null);
      setSnackbar({
        open: true,
        message: `Curso "${courseToDelete.title}" eliminado correctamente`,
        severity: "success",
      });
    }
  };

  const handleCancelDelete = () => {
    setOpenDialog(false);
    setCourseToDelete(null);
    setSnackbar({
      open: true,
      message: "Operación cancelada",
      severity: "info",
    });
  };

  const handleCloseSnackbar = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  return (
    <>
      <div className={styles.coursesList}>
        {courses.map((course) => (
          <div key={course.id} className={styles.courseCard}>
            <div className={styles.courseHeader}>
              <h3 className={styles.courseTitle}>{course.title}</h3>
              <IconButton
                className={styles.deleteButton}
                onClick={() => handleDeleteClick(course)}
                size="small"
              >
                <CloseIcon />
              </IconButton>
            </div>
            <p className={styles.courseDescription}>{course.description}</p>
            <div className={styles.courseActions}>
              <button
                className={styles.actionButton}
                onClick={() => handleAccessCourse(course)}
              >
                Acceder
              </button>
            </div>
          </div>
        ))}
      </div>

      <Dialog
        open={openDialog}
        onClose={handleCancelDelete}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {"¿Estás seguro de que quieres eliminar este curso?"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Esta acción no se puede deshacer. Se eliminará permanentemente el
            curso "{courseToDelete?.title}".
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete} color="primary">
            Cancelar
          </Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            Eliminar
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
}
