.sidebar {
  width: 250px;
  height: 100vh;
  position: fixed;
  top: 92px;
  left: 0;
  background-color: var(--bg-primary);
  padding: 1rem;
  transition: all 0.3s ease;
  border-right: 1px solid var(--border-color);
  z-index: 90;
}

.navItems {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.menuItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.menuItem:hover {
  background-color: var(--bg-hover);
}

.menuItem.active {
  background-color: var(--bg-hover);
  color: var(--primary);
}

.menuIcon {
  font-size: 1.25rem;
  color: var(--text-secondary);
  transition: color 0.2s ease;
}

.menuItem:hover .menuIcon,
.menuItem.active .menuIcon {
  color: var(--primary);
}

.menuText {
  font-size: 0.875rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .sidebar {
    display: none;
  }
}
