"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import HomeOutlinedIcon from "@mui/icons-material/HomeOutlined";
import SchoolOutlinedIcon from "@mui/icons-material/SchoolOutlined";
import BookOutlinedIcon from "@mui/icons-material/BookOutlined";
import AssessmentOutlinedIcon from "@mui/icons-material/AssessmentOutlined";
import PeopleOutlineIcon from "@mui/icons-material/PeopleOutline";
import WorkOutlineIcon from "@mui/icons-material/WorkOutline"; // Importa el icono para trabajos
import GradingIcon from "@mui/icons-material/Grading"; // Importa el icono para evaluaciones

import styles from "./Sidebar.module.css";

interface SidebarProps {
  user: any | null;
}

export function Sidebar({ user }: SidebarProps) {
  const pathname = usePathname();

  // Si no hay usuario logado, no mostramos el sidebar
  if (!user) {
    return null;
  }

  const menuItems = [
    {
      href: "/",
      icon: <HomeOutlinedIcon className={styles.menuIcon} />,
      text: "Inicio",
    },
    {
      href: "/courses",
      icon: <SchoolOutlinedIcon className={styles.menuIcon} />,
      text: "Cursos",
    },
    {
      href: "/subjects",
      icon: <BookOutlinedIcon className={styles.menuIcon} />,
      text: "Asignaturas",
    },
    {
      href: "/criterias",
      icon: <AssessmentOutlinedIcon className={styles.menuIcon} />,
      text: "Criterios",
    },
    {
      href: "/indicators",
      icon: <AssessmentOutlinedIcon className={styles.menuIcon} />,
      text: "Indicadores",
    },
    {
      href: "/students",
      icon: <PeopleOutlineIcon className={styles.menuIcon} />,
      text: "Alumnos",
    },
    {
      href: "/homeworks",
      icon: <WorkOutlineIcon className={styles.menuIcon} />,
      text: "Trabajos",
    },
    {
      href: "/evaluations",
      icon: <GradingIcon className={styles.menuIcon} />,
      text: "Evaluaciones",
    },
  ];

  return (
    <aside className={styles.sidebar}>
      <nav className={styles.navItems}>
        {menuItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={`${styles.menuItem} ${
              pathname === item.href ? styles.active : ""
            }`}
          >
            {item.icon}
            <span className={styles.menuText}>{item.text}</span>
          </Link>
        ))}
      </nav>
    </aside>
  );
}
