import React from "react";
import { HeroVariantType } from "../HeroSection/HeroSection.types";

interface IllustrationProps {
  variant: HeroVariantType;
  width?: number;
  height?: number;
}

export const Illustration: React.FC<IllustrationProps> = ({
  variant,
  width,
  height,
}) => {
  const getIllustrationPath = () => `/images/${variant}.svg`;

  return (
    <img
      src={getIllustrationPath()}
      alt={`${variant} illustration`}
      width={width ?? 300}
      height={height ?? 187}
    />
  );
};
