import { create<PERSON>ontext, use<PERSON>ontext, use<PERSON>emo, ReactNode } from "react";
import { DomainApp } from "../../domain";

interface DomainContextType {
  domain: DomainApp;
}

interface DomainProviderProps {
  children: ReactNode;
}

const DomainContext = createContext<DomainContextType | undefined>(undefined);

function DomainProvider({ children }: Readonly<DomainProviderProps>) {
  const domainApp = useMemo(() => DomainApp.create(), []);

  const value = useMemo(
    () => ({
      domain: domainApp,
    }),
    [domainApp]
  );

  return (
    <DomainContext.Provider value={value}>{children}</DomainContext.Provider>
  );
}

function useDomain(): DomainContextType {
  const context = useContext(DomainContext);
  if (context === undefined) {
    throw new Error(`useDomain must be used within a DomainProvider`);
  }
  return context;
}

export { DomainProvider, useDomain };
