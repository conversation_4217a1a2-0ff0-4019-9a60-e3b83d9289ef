"use client";

import { useEffect, useState } from "react";
import { TopBar } from "@/components/TopBar/TopBar";
import { Sidebar } from "@/components/Sidebar/Sidebar";
import { DomainProvider, useDomain } from "@/components/Context";
import { ThemeProvider } from "@/components/Context/ThemeContext";
import { User, Session } from "@supabase/supabase-js";
import "@/styles/globals.css";
import styles from "./ClientLayout.module.css";
import { useRouter, usePathname } from "next/navigation";

function ClientLayoutContent({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();
  const { domain } = useDomain();

  const privateRoutes = [
    "/homeworks",
    "/evaluations",
    "/courses",
    "/criterias",
    "/indicators",
    "/students",
    "/subjects",
    "/dashboard",
    "/admin",
    "/profile",
    "/settings",
  ];

  const isPrivateRoute = privateRoutes.some(
    (route) => pathname === route || pathname?.startsWith(`${route}/`)
  );

  useEffect(() => {
    const fetchSession = async () => {
      setIsLoading(true);
      try {
        await domain.refreshSessionUseCase.execute();

        const { session, error } = await domain.getSessionUseCase.execute();
        if (error) {
          console.error("ClientLayout - Error al obtener la sesión:", error);
          setUser(null);
        } else if (session?.user) {
          console.log(
            "ClientLayout - Usuario autenticado:",
            session.user.email
          );
          setUser(session.user);
        } else {
          console.log("ClientLayout - No hay sesión activa");
          setUser(null);
        }
      } catch (err) {
        console.error(
          "ClientLayout - Error inesperado al obtener la sesión:",
          err
        );
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSession();

    const subscription = domain.onAuthStateChangeUseCase.execute({
      callback: (
        event:
          | "INITIAL_SESSION"
          | "SIGNED_IN"
          | "SIGNED_OUT"
          | "TOKEN_REFRESHED"
          | "USER_UPDATED"
          | "PASSWORD_RECOVERY",
        session: Session | null
      ) => {
        console.log("ClientLayout - Cambio en la autenticación:", event);
        if (session?.user) {
          console.log(
            "ClientLayout - Usuario autenticado:",
            session.user.email
          );
          setUser(session.user);
        } else {
          console.log("ClientLayout - Usuario no autenticado");
          setUser(null);
        }
        setIsLoading(false);
      },
    });

    return () => {
      if (subscription?.data?.subscription) {
        subscription.data.subscription.unsubscribe();
      }
    };
  }, [domain]);

  useEffect(() => {
    if (!isLoading && isPrivateRoute && !user) {
      console.log(`ClientLayout - Redirigiendo a login desde: ${pathname}`);
      // Asegurarse de que pathname sea una ruta válida
      const redirectPath = pathname || "/";
      console.log(`ClientLayout - Ruta para redirección: ${redirectPath}`);
      router.push(`/login?redirectTo=${encodeURIComponent(redirectPath)}`);
    }
  }, [isLoading, isPrivateRoute, user, pathname, router]);

  return (
    <ThemeProvider>
      <div className={styles.container}>
        <TopBar
          user={
            user
              ? {
                  user_metadata: {
                    full_name:
                      user.user_metadata?.name || user.email || "Usuario",
                    avatar_url: user.user_metadata?.avatar_url || "",
                  },
                }
              : null
          }
        />
        <Sidebar user={user} />
        <main className={`${styles.main} ${!user ? styles.mainNoSidebar : ""}`}>
          {children}
        </main>
      </div>
    </ThemeProvider>
  );
}

export function ClientLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <DomainProvider>
      <ClientLayoutContent>{children}</ClientLayoutContent>
    </DomainProvider>
  );
}
