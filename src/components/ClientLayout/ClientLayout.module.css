.container {
  min-height: 100vh;
  background-color: var(--bg-app);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.main {
  padding-top: 5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  background-color: var(--bg-app);
  transition: all 0.3s ease;
}

@media (min-width: 768px) {
  .main {
    padding: 108px 48px 0 306px; /* 250px del sidebar + 16px de padding */
  }

  .mainNoSidebar {
    padding: 108px 48px 0 48px; /* Sin sidebar, padding igual en ambos lados */
  }
}
