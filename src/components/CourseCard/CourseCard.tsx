import { Course } from "@/domain/courses/entities/Course";
import { useRouter } from "next/navigation";
import { useDomain } from "@/components/Context/DomainContext";
import { ConfirmModal } from "@/components/ConfirmModal/ConfirmModal";
import { ErrorModal } from "@/components/ErrorModal/ErrorModal";
import { useState } from "react";
import { CloseIcon } from "@/components/icons";
import styles from "./CourseCard.module.css";

interface CourseCardProps {
  course: Course;
  onDelete: (courseId: string) => void;
}

export function CourseCard({ course, onDelete }: Readonly<CourseCardProps>) {
  const router = useRouter();
  const { domain } = useDomain();
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDelete = async () => {
    try {
      await domain.deleteCourseUseCase.execute({ courseId: course.id });
      onDelete(course.id);
      setShowConfirmDelete(false);
    } catch (error) {
      console.error("Error al eliminar el curso:", error);
      setError("Error al eliminar el curso");
    }
  };

  return (
    <div className={styles.card}>
      <button
        className="btn-close"
        onClick={() => setShowConfirmDelete(true)}
        aria-label={`Eliminar curso ${course.title}`}
      >
        <CloseIcon />
      </button>
      <h2 className={styles.title}>{course.title}</h2>
      <p className={styles.description}>{course.description}</p>
      <div className={styles.footer}>
        <span className={styles.slug}>{course.slug}</span>
        <div className={styles.actions}>
          <button
            className="btn-primary btn-sm"
            onClick={() => router.push(`/courses/${course.slug}`)}
          >
            Acceder
          </button>
          <button
            className="btn-secondary btn-sm"
            onClick={() => router.push(`/courses/${course.slug}`)}
          >
            Editar
          </button>
        </div>
      </div>

      <ConfirmModal
        open={showConfirmDelete}
        title="Eliminar curso"
        message="¿Estás seguro de que quieres eliminar este curso?"
        onConfirm={handleDelete}
        onCancel={() => setShowConfirmDelete(false)}
      />

      <ErrorModal
        open={!!error}
        message={error ?? ""}
        onClose={() => setError(null)}
      />
    </div>
  );
}
