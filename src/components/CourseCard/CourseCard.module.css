.card {
  background-color: var(--bg-primary);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
  position: relative;
}

.card:hover {
  transform: translateY(-2px);
}

.deleteButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: var(--title-color);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
}

.deleteButton:hover {
  color: #e74c3c;
}

.title {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  color: var(--title-color);
  padding-right: 2rem;
}

.description {
  margin: 0 0 1rem 0;
  color: var(--title-color);
  line-height: 1.5;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.slug {
  font-size: 0.875rem;
  color: #999;
}

.actions {
  display: flex;
  gap: 0.5rem;
}

.accessButton,
.editButton {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.accessButton {
  background-color: #2ecc71;
  color: white;
}

.accessButton:hover {
  background-color: #27ae60;
}

.editButton {
  background-color: #4a90e2;
  color: white;
}

.editButton:hover {
  background-color: #357abd;
}
