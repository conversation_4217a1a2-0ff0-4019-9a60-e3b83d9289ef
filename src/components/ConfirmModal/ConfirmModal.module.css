.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 90%;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--title-color);
  margin: 0 0 1rem 0;
}

.message {
  color: #4a4a4a;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.cancelButton,
.confirmButton {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background-color: #f5f5f5;
  color: var(--title-color);
  border: 1px solid #e0e0e0;
}

.cancelButton:hover {
  background-color: #e0e0e0;
}

.confirmButton {
  background-color: #d32f2f;
  color: white;
  border: none;
}

.confirmButton:hover {
  background-color: #c62828;
}
