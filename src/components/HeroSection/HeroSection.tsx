import React from "react";
import { useRouter } from "next/navigation";
import styles from "./HeroSection.module.css";
import { HeroSectionProps, HeroVariant } from "./HeroSection.types";
import { Illustration } from "../Illustration";

export const HeroSection = ({
  title,
  description,
  ctaText,
  ctaUrl,
  variant = HeroVariant.COURSES,
}: HeroSectionProps) => {
  const router = useRouter();

  const renderIllustration = () => {
    switch (variant) {
      case HeroVariant.COURSES:
        return (
          <Illustration
            variant={HeroVariant.COURSES}
            width={300}
            height={180}
          />
        );
      case HeroVariant.SUBJECTS:
        return (
          <Illustration
            variant={HeroVariant.SUBJECTS}
            width={300}
            height={180}
          />
        );
      case HeroVariant.CRITERIAS:
        return (
          <Illustration
            variant={HeroVariant.CRITERIAS}
            width={300}
            height={180}
          />
        );
      case HeroVariant.INDICATORS:
        return (
          <Illustration
            variant={HeroVariant.INDICATORS}
            width={300}
            height={180}
          />
        );
      case HeroVariant.STUDENTS:
        return (
          <Illustration
            variant={HeroVariant.STUDENTS}
            width={300}
            height={180}
          />
        );
      case HeroVariant.HOMEWORKS:
        return (
          <Illustration
            variant={HeroVariant.HOMEWORKS}
            width={300}
            height={180}
          />
        );
      case HeroVariant.EVALUATIONS:
        return (
          <Illustration
            variant={HeroVariant.EVALUATIONS}
            width={300}
            height={180}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className={styles.heroSection}>
      <div className={styles.heroContent}>
        <h1 className={styles.title}>{title}</h1>
        <p className={styles.description}>{description}</p>
        <button
          onClick={() => router.push(ctaUrl)}
          className={styles.createButton}
        >
          {ctaText}
        </button>
      </div>
      <div className={styles.heroImageContainer}>
        <div className={styles.heroImage}>{renderIllustration()}</div>
      </div>
    </div>
  );
};
