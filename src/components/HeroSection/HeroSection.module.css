/* Hero Section */
.heroSection {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding: 2rem;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.heroContent {
  flex: 1;
  max-width: 600px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--title-color);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.heroImageContainer {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.heroImage {
  max-width: 100%;
  height: auto;
}

/* Create Button */
.createButton {
  padding: 0.75rem 1.5rem;
  background: #0284c7; /* Color sólido más oscuro para mejor contraste */
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 700; /* Aumentado el peso de la fuente */
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(29, 136, 175, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* Sombra más pronunciada */
  letter-spacing: 0.5px; /* Mejor legibilidad */
}

.createButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(29, 136, 175, 0.3);
  background: #0369a1; /* Color hover más oscuro */
}

/* Responsive */
@media (max-width: 768px) {
  .heroSection {
    flex-direction: column;
    padding: 1.5rem;
  }

  .heroContent {
    max-width: 100%;
    margin-bottom: 2rem;
  }

  .title {
    font-size: 2rem;
  }

  .description {
    font-size: 1rem;
  }
}
