const HeroVariant = {
  COURSES: "courses",
  CRITERIAS: "criterias",
  INDICATORS: "indicators",
  SUBJECTS: "subjects",
  STUDENTS: "students",
  HOMEWORKS: "homeworks",
  EVALUATIONS: "evaluations",
} as const;

type HeroVariantType = (typeof HeroVariant)[keyof typeof HeroVariant];
interface HeroSectionProps {
  title: string;
  description: string;
  ctaText: string;
  ctaUrl: string;
  variant?: HeroVariantType;
}

export type { HeroSectionProps, HeroVariantType };
export { HeroVariant };
