import {
  Button,
  <PERSON>alog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  TextField,
} from "@mui/material";
import { useState } from "react";

interface AddSubjectProps {
  onAddSubject: (name: string, courseId: string) => Promise<void>;
  courses: Array<{ id: string; title: string }>;
}

export function AddSubject({ onAddSubject, courses }: AddSubjectProps) {
  const [open, setOpen] = useState(false);
  const [name, setName] = useState("");
  const [courseId, setCourseId] = useState("");

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setName("");
    setCourseId("");
  };

  const handleSubmit = async () => {
    if (name && courseId) {
      await onAddSubject(name, courseId);
      handleClose();
    }
  };

  return (
    <>
      <Button variant="contained" onClick={handleClickOpen}>
        <PERSON><PERSON><PERSON>
      </Button>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Añadir Nueva Materia</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Nombre de la Materia"
            type="text"
            fullWidth
            variant="outlined"
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
          <TextField
            select
            margin="dense"
            label="Curso"
            fullWidth
            variant="outlined"
            value={courseId}
            onChange={(e) => setCourseId(e.target.value)}
            SelectProps={{
              native: true,
            }}
          >
            <option value="">Selecciona un curso</option>
            {courses.map((course) => (
              <option key={course.id} value={course.id}>
                {course.title}
              </option>
            ))}
          </TextField>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancelar</Button>
          <Button onClick={handleSubmit} variant="contained">
            Crear
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
