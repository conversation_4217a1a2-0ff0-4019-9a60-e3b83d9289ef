.card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.card:hover {
  border-color: var(--primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.content {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

.footer {
  margin-top: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1rem;
}

.button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primaryButton {
  composes: button;
  background-color: var(--primary);
  color: white;
}

.primaryButton:hover {
  background-color: var(--primary-dark);
}

.secondaryButton {
  composes: button;
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.secondaryButton:hover {
  background-color: var(--bg-secondary);
}
