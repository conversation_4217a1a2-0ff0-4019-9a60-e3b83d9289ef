"use client";

import { useRouter } from "next/navigation";
import styles from "./Breadcrumbs.module.css";

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
}

export function Breadcrumbs({ items }: BreadcrumbsProps) {
  const router = useRouter();

  const handleClick = (href?: string) => {
    if (href) {
      router.push(href);
    }
  };

  return (
    <nav className={styles.breadcrumbs}>
      {items.map((item, index) => (
        <div key={index} className={styles.breadcrumbItem}>
          {index > 0 && <span className={styles.separator}>/</span>}
          <button
            onClick={() => handleClick(item.href)}
            className={`${styles.breadcrumbLink} ${
              !item.href ? styles.current : ""
            }`}
          >
            {item.label}
          </button>
        </div>
      ))}
    </nav>
  );
}
