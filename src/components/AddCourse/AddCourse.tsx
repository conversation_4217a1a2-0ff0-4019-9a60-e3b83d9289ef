"use client";

import { useState } from "react";
import { TextField, Button } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import styles from "./AddCourse.module.css";

interface AddCourseProps {
  onAddCourse: (title: string, description: string) => Promise<void>;
}

export const AddCourse = ({ onAddCourse }: Readonly<AddCourseProps>) => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [showAddCourse, setShowAddCourse] = useState(false);

  const handleAddCourse = async () => {
    if (!title.trim()) return;
    await onAddCourse(title, description);
    setTitle("");
    setDescription("");
    setShowAddCourse(false);
  };

  if (!showAddCourse) {
    return (
      <Button
        variant="contained"
        startIcon={<AddIcon />}
        onClick={() => setShowAddCourse(true)}
        className={styles.addButton}
        color="primary"
      >
        Añadir curso
      </Button>
    );
  }

  return (
    <div className={styles.addCourseContainer}>
      <div className={styles.formContainer}>
        <TextField
          fullWidth
          label="Título del curso"
          variant="outlined"
          size="small"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className={styles.input}
        />
        <TextField
          fullWidth
          label="Descripción del curso"
          variant="outlined"
          size="small"
          multiline
          rows={3}
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          className={styles.input}
        />
        <div className={styles.ctaContainer}>
          <Button
            variant="contained"
            onClick={handleAddCourse}
            disabled={!title.trim()}
          >
            Guardar
          </Button>
          <Button
            variant="outlined"
            onClick={() => setShowAddCourse(false)}
            color="inherit"
          >
            Cancelar
          </Button>
        </div>
      </div>
    </div>
  );
};
