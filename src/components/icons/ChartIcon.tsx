import React from "react";

interface IconProps {
  className?: string;
  width?: number;
  height?: number;
  style?: React.CSSProperties;
}

export const ChartIcon: React.FC<IconProps> = ({
  className,
  width = 24,
  height = 24,
  style,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
    >
      <path
        d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM9 17H7V10H9V17ZM13 17H11V7H13V17ZM17 17H15V13H17V17Z"
        fill="currentColor"
      />
    </svg>
  );
};
