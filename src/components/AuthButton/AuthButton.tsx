"use client";

import { Button } from "@mui/material";
import { useState } from "react";
import { useDomain } from "@/components/Context";

export const AuthButton = ({
  isLoggedIn,
  redirectTo = "/",
}: {
  isLoggedIn: boolean;
  redirectTo?: string;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { domain } = useDomain();

  console.log("AuthButton - redirectTo:", redirectTo);

  const handleLogin = async () => {
    try {
      setIsLoading(true);
      console.log("AuthButton - Iniciando sesión, redirectTo:", redirectTo);
      const encodedRedirectTo = encodeURIComponent(redirectTo);
      console.log("AuthButton - Encoded redirectTo:", encodedRedirectTo);

      const { error } = await domain.signInWithGoogleUseCase.execute(
        encodedRedirectTo
      );

      if (error) {
        console.error("AuthButton - Error al iniciar sesión:", error);
        alert(`Error al iniciar sesión: ${error.message}`);
      }
    } catch (error) {
      console.error("AuthButton - Error inesperado:", error);
      alert("Ocurrió un error inesperado al iniciar sesión");
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      console.log("AuthButton - Cerrando sesión");
      const { error } = await domain.signOutUseCase.execute();

      if (error) {
        console.error("AuthButton - Error al cerrar sesión:", error);
        alert(`Error al cerrar sesión: ${error.message}`);
      } else {
        console.log("AuthButton - Sesión cerrada correctamente");
        window.location.href = "/";
      }
    } catch (error) {
      console.error("AuthButton - Error inesperado al cerrar sesión:", error);
      alert("Ocurrió un error inesperado al cerrar sesión");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      {!isLoggedIn ? (
        <Button
          variant="contained"
          size="small"
          onClick={handleLogin}
          disabled={isLoading}
        >
          {isLoading ? "Iniciando sesión..." : "Iniciar sesión"}
        </Button>
      ) : (
        <Button
          variant="contained"
          size="small"
          onClick={handleLogout}
          disabled={isLoading}
        >
          {isLoading ? "Cerrando sesión..." : "Cerrar sesión"}
        </Button>
      )}
    </div>
  );
};
