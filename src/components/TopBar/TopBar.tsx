"use client";

import { useRouter } from "next/navigation";
import AssessmentOutlinedIcon from "@mui/icons-material/AssessmentOutlined";
import LightModeIcon from "@mui/icons-material/LightMode";
import DarkModeIcon from "@mui/icons-material/DarkMode";
import { useTheme } from "@/components/Context/ThemeContext";
import { useState } from "react";
import { useDomain } from "@/components/Context";
import styles from "./TopBar.module.css";

interface TopBarProps {
  user: {
    user_metadata: {
      full_name: string;
      avatar_url: string;
    };
  } | null;
}

export const TopBar = ({ user }: Readonly<TopBarProps>) => {
  const { domain } = useDomain();
  const router = useRouter();
  const { isDarkMode, toggleTheme } = useTheme();

  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      const { error } = await domain.signOutUseCase.execute();

      if (error) {
        console.error("Error al cerrar sesión:", error);
        alert(`Error al cerrar sesión: ${error.message}`);
        return;
      }

      router.push("/");
    } catch (err) {
      console.error("Error inesperado al cerrar sesión:", err);
      alert("Ocurrió un error inesperado al cerrar sesión");
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <nav className={styles.topbar}>
      <div className={styles.container}>
        <div className={styles.leftSection}>
          <button
            className={styles.logo}
            onClick={() => router.push("/")}
            aria-label="Ir a la página principal"
            type="button"
          >
            <AssessmentOutlinedIcon className={styles.logoIcon} />
            <span className={styles.logoText}>EvalTool</span>
          </button>
        </div>

        <div className={styles.rightSection}>
          <button
            className={styles.themeToggle}
            onClick={toggleTheme}
            aria-label={
              isDarkMode ? "Cambiar a modo claro" : "Cambiar a modo oscuro"
            }
          >
            {isDarkMode ? <LightModeIcon /> : <DarkModeIcon />}
          </button>
          {user ? (
            <div className={styles.userSection}>
              <img
                src={user.user_metadata.avatar_url}
                alt={user.user_metadata.full_name}
                className={styles.userAvatar}
              />
              <span className={styles.userName}>
                {user.user_metadata.full_name}
              </span>
              <button
                className={styles.logoutButton}
                onClick={handleLogout}
                disabled={isLoggingOut}
              >
                {isLoggingOut ? "Cerrando..." : "Cerrar sesión"}
              </button>
            </div>
          ) : (
            <button
              className={styles.loginButton}
              onClick={() => router.push("/login")}
            >
              Iniciar sesión
            </button>
          )}
        </div>
      </div>
    </nav>
  );
};
