.topbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 92px;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  z-index: 100;
  transition: all 0.3s ease;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
}

.leftSection {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  text-decoration: none;
  background: none;
  border: none;
  padding: 0;
  outline: none;
}

.logo:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: 4px;
}

.logoIcon {
  font-size: 2rem;
  color: var(--primary);
}

.logoText {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.rightSection {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.themeToggle {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.themeToggle:hover {
  background-color: var(--bg-hover);
  color: var(--primary);
}

.userSection {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.userAvatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.userName {
  font-size: 0.875rem;
  color: var(--text-primary);
}

.logoutButton,
.loginButton {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.logoutButton {
  background-color: transparent;
  color: var(--text-primary);
}

.logoutButton:hover {
  background-color: var(--bg-hover);
}

.loginButton {
  background-color: var(--primary);
  color: white;
}

.loginButton:hover {
  background-color: var(--primary-dark);
}

@media (max-width: 768px) {
  .topbar {
    height: 72px;
  }

  .container {
    padding: 0 1rem;
  }

  .logoText {
    display: none;
  }

  .userName {
    display: none;
  }
}
