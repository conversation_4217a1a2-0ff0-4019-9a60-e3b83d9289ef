/* Importar estilos de botones */
@import "./buttons.css";

/* Variables del tema */
:root {
  /* Colores del tema claro */
  --primary: #1d88af;
  --primary-dark: #156785;
  --bg-primary: #ffffff;
  --bg-secondary: #f8f8f8;
  --bg-hover: #f0f0f0;
  --text-primary: #333333;
  --text-secondary: #666666;
  --border-color: #e0e0e0;
  --bg-app: #e8f4ff;
  --title-color: #1e293b;
  --outline-color: #e2e8f0;
}

/* Variables para el tema oscuro */
html.dark-mode {
  --primary: #2196f3;
  --primary-dark: #1976d2;
  --bg-primary: #292929;
  --bg-secondary: rgb(0, 0, 0);
  --bg-hover: #2d2d2d;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --border-color: #404040;
  --bg-app: #0a0a0a;
  --title-color: #aac9fa;
  --outline-color: #6a6a6a;
}

/* Estilos base */
html,
body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--bg-app);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

/* Estilos globales adicionales */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Asegurarse de que los componentes respeten el tema */
button,
input,
select,
textarea {
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border-color: var(--border-color);
  transition: all 0.3s ease;
}

a {
  color: var(--primary);
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary-dark);
}

/* Estilos específicos para componentes comunes */
.card,
.container,
.content {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-color);
  transition: all 0.3s ease;
}

/* Ajustes para el modo oscuro */
html.dark-mode .MuiPaper-root {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

html.dark-mode .MuiButton-root {
  color: var(--text-primary);
}

html.dark-mode .MuiInputBase-root {
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

/* Asegurarse que los diálogos y modales respeten el tema */
html.dark-mode .MuiDialog-paper {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Ajustes para elementos de formulario en modo oscuro */
html.dark-mode input,
html.dark-mode select,
html.dark-mode textarea {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-color);
}
