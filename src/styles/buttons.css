/* Estilos globales para botones */

/* <PERSON><PERSON><PERSON> primario (CTA) */
.btn-primary {
  padding: 0.75rem 1.25rem;
  background: #0284c7; /* Color sólido más oscuro para mejor contraste */
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(29, 136, 175, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(29, 136, 175, 0.3);
  background: #0369a1;
}

/* Botón secundario */
.btn-secondary {
  padding: 0.75rem 1.25rem;
  background-color: rgba(29, 136, 175, 0.1);
  color: var(--primary);
  border: 1px solid var(--primary);
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background-color: rgba(29, 136, 175, 0.2);
  transform: translateY(-2px);
}

/* Botón de peligro (eliminar) */
.btn-danger {
  padding: 0.75rem 1.25rem;
  background-color: rgba(211, 47, 47, 0.1);
  color: #d32f2f;
  border: 1px solid #d32f2f;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-danger:hover {
  background-color: #d32f2f;
  color: white;
  transform: translateY(-2px);
}

/* Botón de cierre (X) */
.btn-close {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  opacity: 1;
  z-index: 10; /* Asegura que el botón esté por encima de otros elementos */
}

.btn-close:hover {
  background-color: rgba(211, 47, 47, 0.1);
  color: #d32f2f;
  opacity: 1;
}

/* Botón de añadir */
.btn-add {
  padding: 0.75rem 1.25rem;
  background: #0284c7;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(29, 136, 175, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-add:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(29, 136, 175, 0.3);
  background: #0369a1;
}

/* Variantes de tamaño */
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* Botón deshabilitado */
.btn-primary:disabled,
.btn-secondary:disabled,
.btn-danger:disabled,
.btn-add:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Botón de texto (sin fondo) */
.btn-text {
  background: transparent;
  color: var(--primary);
  border: none;
  padding: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-text:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Botón con icono */
.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-icon-only {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Responsive */
@media (max-width: 768px) {
  .btn-primary,
  .btn-secondary,
  .btn-danger,
  .btn-add {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }

  .btn-lg {
    padding: 0.8rem 1.5rem;
    font-size: 0.95rem;
  }
}
