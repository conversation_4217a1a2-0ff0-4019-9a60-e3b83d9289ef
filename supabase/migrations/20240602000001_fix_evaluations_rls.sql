-- Verificar si la tabla evaluations tiene RLS habilitado
DO $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM pg_tables
    WHERE schemaname = 'public'
    AND tablename = 'evaluations'
    AND rowsecurity = true
  ) THEN
    -- Si RLS está habilitado, verificar y crear políticas permisivas
    
    -- Eliminar políticas restrictivas existentes si las hay
    DROP POLICY IF EXISTS "Usuarios solo pueden ver sus propias evaluaciones" ON public.evaluations;
    DROP POLICY IF EXISTS "Usuarios solo pueden modificar sus propias evaluaciones" ON public.evaluations;
    DROP POLICY IF EXISTS "Solo administradores pueden modificar evaluaciones" ON public.evaluations;
    
    -- Crear políticas permisivas
    -- Para SELECT
    IF NOT EXISTS (
      SELECT 1
      FROM pg_policies
      WHERE schemaname = 'public'
      AND tablename = 'evaluations'
      AND operation = 'SELECT'
    ) THEN
      CREATE POLICY "Cualquier usuario autenticado puede leer evaluaciones"
      ON public.evaluations FOR SELECT
      TO authenticated
      USING (true);
    END IF;
    
    -- Para INSERT
    IF NOT EXISTS (
      SELECT 1
      FROM pg_policies
      WHERE schemaname = 'public'
      AND tablename = 'evaluations'
      AND operation = 'INSERT'
    ) THEN
      CREATE POLICY "Cualquier usuario autenticado puede insertar evaluaciones"
      ON public.evaluations FOR INSERT
      TO authenticated
      WITH CHECK (true);
    END IF;
    
    -- Para UPDATE
    IF NOT EXISTS (
      SELECT 1
      FROM pg_policies
      WHERE schemaname = 'public'
      AND tablename = 'evaluations'
      AND operation = 'UPDATE'
    ) THEN
      CREATE POLICY "Cualquier usuario autenticado puede actualizar evaluaciones"
      ON public.evaluations FOR UPDATE
      TO authenticated
      USING (true);
    END IF;
    
    -- Para DELETE
    IF NOT EXISTS (
      SELECT 1
      FROM pg_policies
      WHERE schemaname = 'public'
      AND tablename = 'evaluations'
      AND operation = 'DELETE'
    ) THEN
      CREATE POLICY "Cualquier usuario autenticado puede eliminar evaluaciones"
      ON public.evaluations FOR DELETE
      TO authenticated
      USING (true);
    END IF;
  END IF;
END $$;
