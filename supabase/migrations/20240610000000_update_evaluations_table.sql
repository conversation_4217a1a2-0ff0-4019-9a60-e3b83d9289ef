-- A<PERSON>dir columna homework_id si no existe
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'evaluations'
    AND column_name = 'homework_id'
  ) THEN
    ALTER TABLE public.evaluations
    ADD COLUMN homework_id uuid REFERENCES public.homeworks(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Eliminar la restricción única existente
ALTER TABLE public.evaluations
DROP CONSTRAINT IF EXISTS evaluations_student_id_indicator_id_subject_id_key;

-- Crear una nueva restricción única que incluya homework_id solo si no existe
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint
    WHERE conname = 'evaluations_student_id_indicator_id_subject_id_homework_id_key'
    AND conrelid = 'public.evaluations'::regclass
  ) THEN
    ALTER TABLE public.evaluations
    ADD CONSTRAINT evaluations_student_id_indicator_id_subject_id_homework_id_key
    UNIQUE (student_id, indicator_id, subject_id, homework_id);
  END IF;
END $$;

-- <PERSON><PERSON>r índice para homework_id
CREATE INDEX IF NOT EXISTS evaluations_homework_id_idx ON public.evaluations(homework_id);