-- Script para verificar y corregir las restricciones de la tabla evaluations

-- Verificar si la columna homework_id es NOT NULL
DO $$
BEGIN
  -- Verificar si la columna homework_id es NOT NULL
  IF EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'evaluations'
    AND column_name = 'homework_id'
    AND is_nullable = 'YES'
  ) THEN
    -- Actualizar registros existentes que tengan homework_id NULL
    -- Primero, verificar si existe la tabla homeworks
    IF EXISTS (
      SELECT 1
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name = 'homeworks'
    ) THEN
      -- Obtener el primer homework_id disponible para asignar a registros con NULL
      DECLARE
        default_homework_id UUID;
      BEGIN
        SELECT id INTO default_homework_id FROM public.homeworks LIMIT 1;
        
        IF default_homework_id IS NOT NULL THEN
          -- Actualizar registros con homework_id NULL
          UPDATE public.evaluations
          SET homework_id = default_homework_id
          WHERE homework_id IS NULL;
          
          -- Hacer la columna NOT NULL
          ALTER TABLE public.evaluations
          ALTER COLUMN homework_id SET NOT NULL;
        END IF;
      END;
    END IF;
  END IF;
END $$;

-- Listar todas las restricciones de la tabla evaluations
SELECT conname, contype, pg_get_constraintdef(oid)
FROM pg_constraint
WHERE conrelid = 'public.evaluations'::regclass;