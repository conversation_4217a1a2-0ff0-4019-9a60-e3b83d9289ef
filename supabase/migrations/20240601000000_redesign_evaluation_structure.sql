-- <PERSON><PERSON>, vamos a crear una tabla para los niveles de evaluación
-- Esta tabla contendrá los 4 niveles posibles (1-4) con sus descripciones genéricas
CREATE TABLE IF NOT EXISTS public.evaluation_levels (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  level_number INTEGER NOT NULL CHECK (level_number BETWEEN 1 AND 4),
  title TEXT NOT NULL,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(level_number)
);

-- Insertar los niveles por defecto solo si no existen
DO $$
BEGIN
  -- Nivel 1
  IF NOT EXISTS (SELECT 1 FROM public.evaluation_levels WHERE level_number = 1) THEN
    INSERT INTO public.evaluation_levels (level_number, title) VALUES (1, 'Nivel básico');
  END IF;

  -- Nivel 2
  IF NOT EXISTS (SELECT 1 FROM public.evaluation_levels WHERE level_number = 2) THEN
    INSERT INTO public.evaluation_levels (level_number, title) VALUES (2, 'Nivel intermedio');
  END IF;

  -- Nivel 3
  IF NOT EXISTS (SELECT 1 FROM public.evaluation_levels WHERE level_number = 3) THEN
    INSERT INTO public.evaluation_levels (level_number, title) VALUES (3, 'Nivel avanzado');
  END IF;

  -- Nivel 4
  IF NOT EXISTS (SELECT 1 FROM public.evaluation_levels WHERE level_number = 4) THEN
    INSERT INTO public.evaluation_levels (level_number, title) VALUES (4, 'Nivel experto');
  END IF;
END $$;

-- Modificar la tabla de indicadores para simplificarla
-- Los indicadores ahora solo tendrán información básica
ALTER TABLE public.indicators DROP COLUMN IF EXISTS is_active;

-- Crear una tabla para las descripciones específicas de cada nivel por indicador
-- Esto permite tener descripciones personalizadas para cada nivel de cada indicador
CREATE TABLE IF NOT EXISTS public.indicator_level_descriptions (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  indicator_id uuid NOT NULL REFERENCES public.indicators(id) ON DELETE CASCADE,
  level_id uuid NOT NULL REFERENCES public.evaluation_levels(id) ON DELETE CASCADE,
  description TEXT NOT NULL,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(indicator_id, level_id)
);

-- Modificar la tabla de evaluaciones para que guarde directamente el nivel y la descripción
-- Esto evita tener que hacer joins complejos para obtener la descripción
ALTER TABLE public.evaluations ADD COLUMN IF NOT EXISTS level_number INTEGER;
ALTER TABLE public.evaluations ADD COLUMN IF NOT EXISTS level_description TEXT;

-- Crear una tabla para los resúmenes de evaluación por criterio
-- Esta tabla se actualizará automáticamente mediante triggers cuando se creen/actualicen evaluaciones
CREATE TABLE IF NOT EXISTS public.criteria_evaluation_summaries (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id uuid NOT NULL REFERENCES public.students(id) ON DELETE CASCADE,
  criteria_id uuid NOT NULL REFERENCES public.criterias(id) ON DELETE CASCADE,
  average_level NUMERIC(3,2) NOT NULL,
  level_description TEXT NOT NULL,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(student_id, criteria_id)
);

-- Crear una función para calcular el resumen de evaluación por criterio
CREATE OR REPLACE FUNCTION update_criteria_evaluation_summary()
RETURNS TRIGGER AS $$
DECLARE
  current_criteria_id uuid;
  avg_level NUMERIC(3,2);
  best_description TEXT;
  rounded_level INTEGER;
BEGIN
  -- Verificar que la evaluación tenga los datos necesarios
  IF NEW.indicator_id IS NULL OR NEW.student_id IS NULL OR NEW.level_number IS NULL THEN
    RETURN NEW;
  END IF;

  -- Obtener el criterio_id a partir del indicator_id
  SELECT c.id INTO current_criteria_id
  FROM criterias c
  JOIN indicators i ON i.criteria_id = c.id
  WHERE i.id = NEW.indicator_id;

  -- Si no se encuentra el criterio, salir
  IF current_criteria_id IS NULL THEN
    RETURN NEW;
  END IF;

  -- Calcular el promedio de nivel para este estudiante y criterio
  SELECT AVG(e.level_number) INTO avg_level
  FROM evaluations e
  JOIN indicators i ON e.indicator_id = i.id
  WHERE i.criteria_id = current_criteria_id
  AND e.student_id = NEW.student_id
  AND e.level_number IS NOT NULL;

  -- Si no hay evaluaciones válidas, salir
  IF avg_level IS NULL THEN
    RETURN NEW;
  END IF;

  -- Redondear el nivel para buscar la mejor descripción
  rounded_level := ROUND(avg_level);

  -- Buscar la mejor descripción (la del nivel exacto al promedio redondeado)
  SELECT e.level_description INTO best_description
  FROM evaluations e
  JOIN indicators i ON e.indicator_id = i.id
  WHERE i.criteria_id = current_criteria_id
  AND e.student_id = NEW.student_id
  AND e.level_number = rounded_level
  AND e.level_description IS NOT NULL
  ORDER BY e.level_number DESC
  LIMIT 1;

  -- Si no encontramos una descripción exacta, buscar la más cercana
  IF best_description IS NULL THEN
    SELECT e.level_description INTO best_description
    FROM evaluations e
    JOIN indicators i ON e.indicator_id = i.id
    WHERE i.criteria_id = current_criteria_id
    AND e.student_id = NEW.student_id
    AND e.level_number <= rounded_level
    AND e.level_description IS NOT NULL
    ORDER BY e.level_number DESC
    LIMIT 1;
  END IF;

  -- Si aún no tenemos descripción, usar una genérica
  IF best_description IS NULL THEN
    SELECT title INTO best_description
    FROM evaluation_levels
    WHERE level_number = rounded_level;

    -- Si aún así no hay descripción, usar un valor predeterminado
    IF best_description IS NULL THEN
      best_description := 'Nivel ' || rounded_level;
    END IF;
  END IF;

  -- Insertar o actualizar el resumen
  INSERT INTO criteria_evaluation_summaries (
    student_id,
    criteria_id,
    average_level,
    level_description
  )
  VALUES (
    NEW.student_id,
    current_criteria_id,
    avg_level,
    best_description
  )
  ON CONFLICT (student_id, criteria_id)
  DO UPDATE SET
    average_level = EXCLUDED.average_level,
    level_description = EXCLUDED.level_description,
    updated_at = NOW();

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Crear un trigger para actualizar el resumen cuando se crea o actualiza una evaluación
DO $$
BEGIN
  -- Verificar si el trigger ya existe
  IF NOT EXISTS (
    SELECT 1
    FROM pg_trigger
    WHERE tgname = 'update_criteria_summary_after_evaluation'
    AND tgrelid = 'public.evaluations'::regclass
  ) THEN
    -- Si no existe, crearlo
    EXECUTE 'CREATE TRIGGER update_criteria_summary_after_evaluation
             AFTER INSERT OR UPDATE ON evaluations
             FOR EACH ROW
             EXECUTE FUNCTION update_criteria_evaluation_summary()';
  END IF;
END $$;

-- Crear un índice para mejorar el rendimiento de las consultas
CREATE INDEX IF NOT EXISTS idx_evaluations_student_indicator ON evaluations (student_id, indicator_id);
CREATE INDEX IF NOT EXISTS idx_criteria_summaries_student ON criteria_evaluation_summaries (student_id);

-- Crear una función para migrar los datos existentes
CREATE OR REPLACE FUNCTION migrate_existing_evaluations()
RETURNS void AS $$
DECLARE
  eval RECORD;
  level_num INTEGER;
  level_desc TEXT;
BEGIN
  FOR eval IN SELECT * FROM evaluations LOOP
    -- Convertir el level_id (UUID) a level_number (INTEGER)
    -- Primero obtenemos el level_number de la tabla de niveles
    BEGIN
      -- Intentar obtener el nivel directamente de la tabla de niveles si existe
      SELECT level_number INTO level_num
      FROM indicator_levels
      WHERE id = eval.level_id;

      EXCEPTION WHEN OTHERS THEN
        -- Si falla, asignar un valor basado en el orden (1-4)
        -- Asumimos que level_id es un UUID que representa un nivel del 1 al 4
        level_num := CASE
          WHEN eval.level_id IS NOT NULL THEN
            -- Extraer el último dígito del UUID y usarlo como nivel (1-4)
            -- Si no es un número válido, usar 1 como valor predeterminado
            GREATEST(1, LEAST(4, (ascii(right(eval.level_id::text, 1)) % 4) + 1))
          ELSE 1 -- Valor predeterminado si level_id es NULL
        END;
    END;

    -- Obtener la descripción del nivel genérico
    SELECT title INTO level_desc
    FROM evaluation_levels
    WHERE level_number = level_num;

    -- Si no se encuentra una descripción, usar un valor predeterminado
    IF level_desc IS NULL THEN
      level_desc := 'Nivel ' || level_num;
    END IF;

    -- Actualizar la evaluación con el nivel y la descripción
    UPDATE evaluations
    SET
      level_number = level_num,
      level_description = level_desc
    WHERE id = eval.id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Crear una función para migrar los niveles de indicadores existentes
CREATE OR REPLACE FUNCTION migrate_indicator_levels()
RETURNS void AS $$
DECLARE
  indicator_rec RECORD;
  level_rec RECORD;
  level_id uuid;
BEGIN
  -- Intentar migrar los niveles de indicadores si existe la tabla indicator_levels
  BEGIN
    -- Verificar si existe la tabla indicator_levels
    PERFORM 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'indicator_levels';

    -- Si llegamos aquí, la tabla existe
    FOR indicator_rec IN SELECT DISTINCT indicator_id FROM indicator_levels LOOP
      -- Para cada indicador, obtener sus niveles
      FOR level_rec IN
        SELECT * FROM indicator_levels
        WHERE indicator_id = indicator_rec.indicator_id
      LOOP
        -- Obtener el ID del nivel genérico correspondiente
        SELECT id INTO level_id FROM evaluation_levels
        WHERE level_number = level_rec.level_number;

        -- Si encontramos el nivel genérico, insertar la descripción específica
        IF level_id IS NOT NULL THEN
          INSERT INTO indicator_level_descriptions (
            indicator_id, level_id, description
          ) VALUES (
            level_rec.indicator_id,
            level_id,
            level_rec.title
          )
          ON CONFLICT (indicator_id, level_id) DO NOTHING;
        END IF;
      END LOOP;
    END LOOP;

    EXCEPTION WHEN OTHERS THEN
      -- Si hay algún error, simplemente continuamos
      RAISE NOTICE 'No se pudo migrar los niveles de indicadores: %', SQLERRM;
  END;
END;
$$ LANGUAGE plpgsql;

-- Ejecutar las migraciones de datos
SELECT migrate_existing_evaluations();
SELECT migrate_indicator_levels();

-- Actualizar los resúmenes de evaluación por criterio para todos los estudiantes
CREATE OR REPLACE FUNCTION update_all_criteria_summaries()
RETURNS void AS $$
DECLARE
  student_rec RECORD;
  criteria_rec RECORD;
  avg_level NUMERIC(3,2);
  best_description TEXT;
  rounded_level INTEGER;
BEGIN
  -- Eliminar todos los resúmenes existentes para empezar desde cero
  DELETE FROM criteria_evaluation_summaries;

  -- Para cada combinación de estudiante y criterio
  FOR student_rec IN SELECT id FROM students LOOP
    FOR criteria_rec IN SELECT id FROM criterias LOOP
      -- Calcular el promedio de nivel para este estudiante y criterio
      SELECT AVG(e.level_number) INTO avg_level
      FROM evaluations e
      JOIN indicators i ON e.indicator_id = i.id
      WHERE i.criteria_id = criteria_rec.id
      AND e.student_id = student_rec.id
      AND e.level_number IS NOT NULL;

      -- Si hay evaluaciones para este estudiante y criterio
      IF avg_level IS NOT NULL THEN
        -- Redondear el nivel para buscar la mejor descripción
        rounded_level := ROUND(avg_level);

        -- Buscar la mejor descripción (la del nivel exacto al promedio redondeado)
        SELECT e.level_description INTO best_description
        FROM evaluations e
        JOIN indicators i ON e.indicator_id = i.id
        WHERE i.criteria_id = criteria_rec.id
        AND e.student_id = student_rec.id
        AND e.level_number = rounded_level
        AND e.level_description IS NOT NULL
        ORDER BY e.level_number DESC
        LIMIT 1;

        -- Si no encontramos una descripción exacta, buscar la más cercana
        IF best_description IS NULL THEN
          SELECT e.level_description INTO best_description
          FROM evaluations e
          JOIN indicators i ON e.indicator_id = i.id
          WHERE i.criteria_id = criteria_rec.id
          AND e.student_id = student_rec.id
          AND e.level_number <= rounded_level
          AND e.level_description IS NOT NULL
          ORDER BY e.level_number DESC
          LIMIT 1;
        END IF;

        -- Si aún no tenemos descripción, usar una genérica
        IF best_description IS NULL THEN
          SELECT title INTO best_description
          FROM evaluation_levels
          WHERE level_number = rounded_level;

          -- Si aún así no hay descripción, usar un valor predeterminado
          IF best_description IS NULL THEN
            best_description := 'Nivel ' || rounded_level;
          END IF;
        END IF;

        -- Insertar el resumen
        INSERT INTO criteria_evaluation_summaries (
          student_id,
          criteria_id,
          average_level,
          level_description
        )
        VALUES (
          student_rec.id,
          criteria_rec.id,
          avg_level,
          best_description
        );
      END IF;
    END LOOP;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Ejecutar la actualización de resúmenes
SELECT update_all_criteria_summaries();

-- Crear políticas RLS para las nuevas tablas
ALTER TABLE public.evaluation_levels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.indicator_level_descriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.criteria_evaluation_summaries ENABLE ROW LEVEL SECURITY;

-- Políticas para evaluation_levels
CREATE POLICY "Cualquier usuario autenticado puede leer niveles de evaluación"
ON public.evaluation_levels FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Solo administradores pueden modificar niveles de evaluación"
ON public.evaluation_levels FOR ALL
TO authenticated
USING (auth.jwt() ->> 'role' = 'admin');

-- Políticas para indicator_level_descriptions
CREATE POLICY "Cualquier usuario autenticado puede leer descripciones de niveles"
ON public.indicator_level_descriptions FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Solo administradores pueden modificar descripciones de niveles"
ON public.indicator_level_descriptions FOR ALL
TO authenticated
USING (auth.jwt() ->> 'role' = 'admin');

-- Políticas para criteria_evaluation_summaries
CREATE POLICY "Cualquier usuario autenticado puede leer resúmenes de evaluación"
ON public.criteria_evaluation_summaries FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Cualquier usuario autenticado puede insertar resúmenes de evaluación"
ON public.criteria_evaluation_summaries FOR INSERT
TO authenticated
WITH CHECK (true);

CREATE POLICY "Cualquier usuario autenticado puede actualizar resúmenes de evaluación"
ON public.criteria_evaluation_summaries FOR UPDATE
TO authenticated
USING (true);

CREATE POLICY "Cualquier usuario autenticado puede eliminar resúmenes de evaluación"
ON public.criteria_evaluation_summaries FOR DELETE
TO authenticated
USING (true);
