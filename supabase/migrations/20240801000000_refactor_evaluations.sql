-- Refactor completo de la tabla evaluations para eliminar inconsistencias y duplicidad

-- Eliminar la tabla de evaluaciones existente
DROP TABLE IF EXISTS public.evaluations;

-- Crear una nueva tabla de evaluaciones optimizada
CREATE TABLE IF NOT EXISTS public.evaluations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  homework_id UUID NOT NULL REFERENCES public.homeworks(id) ON DELETE CASCADE,
  student_id UUID NOT NULL REFERENCES public.students(id) ON DELETE CASCADE,
  indicator_id UUID NOT NULL REFERENCES public.indicators(id) ON DELETE CASCADE,
  level_id UUID NOT NULL REFERENCES public.indicator_levels(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  -- Restricción única clara y consistente
  CONSTRAINT evaluations_homework_student_indicator_unique UNIQUE (homework_id, student_id, indicator_id)
);

-- Habilitar RLS
ALTER TABLE public.evaluations ENABLE ROW LEVEL SECURITY;

-- Crear políticas de seguridad
CREATE POLICY "Los usuarios pueden ver sus evaluaciones"
  ON public.evaluations FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Los usuarios pueden crear evaluaciones"
  ON public.evaluations FOR INSERT
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Los usuarios pueden actualizar sus evaluaciones"
  ON public.evaluations FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Los usuarios pueden eliminar sus evaluaciones"
  ON public.evaluations FOR DELETE
  USING (user_id = auth.uid());

-- Crear trigger para actualizar updated_at
CREATE TRIGGER handle_evaluations_updated_at
  BEFORE UPDATE ON public.evaluations
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Crear índices para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS evaluations_homework_id_idx ON public.evaluations(homework_id);
CREATE INDEX IF NOT EXISTS evaluations_student_id_idx ON public.evaluations(student_id);
CREATE INDEX IF NOT EXISTS evaluations_indicator_id_idx ON public.evaluations(indicator_id);
CREATE INDEX IF NOT EXISTS evaluations_user_id_idx ON public.evaluations(user_id);

-- Verificar y optimizar la tabla homework_criteria
ALTER TABLE public.homework_criteria DROP CONSTRAINT IF EXISTS unique_homework_criteria;
ALTER TABLE public.homework_criteria ADD CONSTRAINT unique_homework_criteria UNIQUE (homework_id, criteria_id);

-- Asegurar que los índices existan
CREATE INDEX IF NOT EXISTS idx_homework_criteria_homework_id ON public.homework_criteria(homework_id);
CREATE INDEX IF NOT EXISTS idx_homework_criteria_criteria_id ON public.homework_criteria(criteria_id);
