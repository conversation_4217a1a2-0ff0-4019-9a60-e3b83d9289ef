-- Agregar la columna slug si no existe
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'courses' 
        AND column_name = 'slug'
    ) THEN
        ALTER TABLE courses ADD COLUMN slug TEXT;
    END IF;
END $$;

-- Función para generar slugs
CREATE OR REPLACE FUNCTION generate_slug(title TEXT)
RETURNS TEXT AS $$
BEGIN
  RETURN LOWER(REGEXP_REPLACE(title, '[^a-zA-Z0-9]+', '-', 'g'));
END;
$$ LANGUAGE plpgsql;

-- Actualizar los cursos existentes con slugs
UPDATE courses 
SET slug = generate_slug(title)
WHERE slug IS NULL;

-- Eliminar la función después de usarla
DROP FUNCTION generate_slug;

-- Crear un índice único para el slug
CREATE UNIQUE INDEX IF NOT EXISTS courses_slug_idx ON courses(slug); 