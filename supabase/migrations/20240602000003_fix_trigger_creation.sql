-- Verificar si el trigger ya existe y eliminarlo si es necesario
DO $$
BEGIN
  -- Verificar si el trigger existe
  IF EXISTS (
    SELECT 1
    FROM pg_trigger
    WHERE tgname = 'update_criteria_summary_after_evaluation'
    AND tgrelid = 'public.evaluations'::regclass
  ) THEN
    -- Si existe, eliminarlo
    DROP TRIGGER IF EXISTS update_criteria_summary_after_evaluation ON public.evaluations;
  END IF;
  
  -- Crear el trigger nuevamente
  CREATE TRIGGER update_criteria_summary_after_evaluation
  AFTER INSERT OR UPDATE ON evaluations
  FOR EACH ROW
  EXECUTE FUNCTION update_criteria_evaluation_summary();
END $$;
