-- Eliminar la columna is_active
ALTER TABLE public.indicators DROP COLUMN IF EXISTS is_active;

-- <PERSON><PERSON>r tabla para los niveles de los indicadores
CREATE TABLE IF NOT EXISTS public.indicator_levels (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  indicator_id uuid NOT NULL REFERENCES public.indicators(id) ON DELETE CASCADE,
  level_number INTEGER NOT NULL CHECK (level_number BETWEEN 1 AND 4),
  title TEXT NOT NULL,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(indicator_id, level_number)
);

-- Habilitar RLS en la nueva tabla
ALTER TABLE public.indicator_levels ENABLE ROW LEVEL SECURITY;

-- Crear políticas de seguridad para indicator_levels
CREATE POLICY "Los usuarios pueden ver los niveles de sus indicadores"
  ON public.indicator_levels FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.indicators i
      JOIN public.criterias c ON c.id = i.criteria_id
      WHERE i.id = indicator_levels.indicator_id
      AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Los usuarios pueden crear niveles en sus indicadores"
  ON public.indicator_levels FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.indicators i
      JOIN public.criterias c ON c.id = i.criteria_id
      WHERE i.id = indicator_levels.indicator_id
      AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Los usuarios pueden actualizar los niveles de sus indicadores"
  ON public.indicator_levels FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.indicators i
      JOIN public.criterias c ON c.id = i.criteria_id
      WHERE i.id = indicator_levels.indicator_id
      AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Los usuarios pueden eliminar los niveles de sus indicadores"
  ON public.indicator_levels FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.indicators i
      JOIN public.criterias c ON c.id = i.criteria_id
      WHERE i.id = indicator_levels.indicator_id
      AND c.user_id = auth.uid()
    )
  ); 