-- Agregar user_id a subjects si no existe
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'subjects' 
        AND column_name = 'user_id'
    ) THEN
        ALTER TABLE subjects ADD COLUMN user_id UUID REFERENCES auth.users(id);
    END IF;
END $$;

-- Actualizar las políticas RLS para subjects
DROP POLICY IF EXISTS "Users can view their own subjects" ON subjects;
DROP POLICY IF EXISTS "Users can insert their own subjects" ON subjects;
DROP POLICY IF EXISTS "Users can update their own subjects" ON subjects;
DROP POLICY IF EXISTS "Users can delete their own subjects" ON subjects;

CREATE POLICY "Users can view their own subjects"
ON subjects FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own subjects"
ON subjects FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own subjects"
ON subjects FOR UPDATE
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own subjects"
ON subjects FOR DELETE
TO authenticated
USING (auth.uid() = user_id); 