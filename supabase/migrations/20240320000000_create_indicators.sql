create table if not exists public.indicators (
  id uuid default gen_random_uuid() primary key,
  title text not null,
  criteria_id uuid not null references public.criterias(id) on delete cascade,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Habilitar RLS
alter table public.indicators enable row level security;

-- Crear políticas de seguridad si no existen
do $$
begin
  if not exists (
    select 1 from pg_policies where tablename = 'indicators' and policyname = 'Los usuarios pueden ver los indicadores de sus criterios'
  ) then
    create policy "Los usuarios pueden ver los indicadores de sus criterios"
      on public.indicators for select
      using (
        exists (
          select 1 from public.criterias c
          where c.id = indicators.criteria_id
          and c.user_id = auth.uid()
        )
      );
  end if;

  if not exists (
    select 1 from pg_policies where tablename = 'indicators' and policyname = 'Los usuarios pueden crear indicadores en sus criterios'
  ) then
    create policy "Los usuarios pueden crear indicadores en sus criterios"
      on public.indicators for insert
      with check (
        exists (
          select 1 from public.criterias c
          where c.id = indicators.criteria_id
          and c.user_id = auth.uid()
        )
      );
  end if;

  if not exists (
    select 1 from pg_policies where tablename = 'indicators' and policyname = 'Los usuarios pueden actualizar sus indicadores'
  ) then
    create policy "Los usuarios pueden actualizar sus indicadores"
      on public.indicators for update
      using (
        exists (
          select 1 from public.criterias c
          where c.id = indicators.criteria_id
          and c.user_id = auth.uid()
        )
      );
  end if;

  if not exists (
    select 1 from pg_policies where tablename = 'indicators' and policyname = 'Los usuarios pueden eliminar sus indicadores'
  ) then
    create policy "Los usuarios pueden eliminar sus indicadores"
      on public.indicators for delete
      using (
        exists (
          select 1 from public.criterias c
          where c.id = indicators.criteria_id
          and c.user_id = auth.uid()
        )
      );
  end if;
end $$;

-- Crear función para actualizar updated_at si no existe
create or replace function public.handle_updated_at()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

-- Crear trigger para actualizar updated_at si no existe
do $$
begin
  if not exists (
    select 1 from pg_trigger where tgname = 'handle_indicators_updated_at'
  ) then
    create trigger handle_indicators_updated_at
      before update on public.indicators
      for each row
      execute function public.handle_updated_at();
  end if;
end $$; 