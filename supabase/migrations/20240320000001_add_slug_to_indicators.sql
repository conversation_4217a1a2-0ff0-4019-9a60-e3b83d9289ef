-- Agregar la columna slug a la tabla indicators
ALTER TABLE indicators ADD COLUMN IF NOT EXISTS slug TEXT;

-- Eliminar las funciones si existen
DROP FUNCTION IF EXISTS generate_unique_slug(TEXT);
DROP FUNCTION IF EXISTS generate_base_slug(TEXT);

-- Función para generar el slug base
CREATE OR REPLACE FUNCTION generate_base_slug(title TEXT)
RETURNS TEXT AS $$
BEGIN
  RETURN LOWER(
    REGEXP_REPLACE(
      REGEXP_REPLACE(
        REGEXP_REPLACE(
          title,
          '[^a-zA-Z0-9]+',
          '-',
          'g'
        ),
        '^-+|-+$',
        '',
        'g'
      ),
      '[áäâà]', 'a', 'g'
    )
  );
END;
$$ LANGUAGE plpgsql;

-- Función para generar un slug único
CREATE OR REPLACE FUNCTION generate_unique_slug(title TEXT)
RETURNS TEXT AS $$
DECLARE
  base_slug TEXT;
  unique_slug TEXT;
  counter INTEGER;
BEGIN
  base_slug := generate_base_slug(title);
  unique_slug := base_slug;
  counter := 1;

  -- Intentar con el slug base
  IF NOT EXISTS (SELECT 1 FROM indicators WHERE slug = unique_slug) THEN
    RETURN unique_slug;
  END IF;

  -- Si existe, agregar números hasta encontrar uno único
  LOOP
    unique_slug := base_slug || '-' || counter;
    IF NOT EXISTS (SELECT 1 FROM indicators WHERE slug = unique_slug) THEN
      RETURN unique_slug;
    END IF;
    counter := counter + 1;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Actualizar los indicadores existentes con sus slugs únicos
UPDATE indicators
SET slug = generate_unique_slug(title)
WHERE slug IS NULL;

-- Hacer la columna slug NOT NULL después de actualizar los registros existentes
ALTER TABLE indicators ALTER COLUMN slug SET NOT NULL;

-- Crear un índice único para el slug
CREATE UNIQUE INDEX IF NOT EXISTS indicators_slug_idx ON indicators(slug);

-- Eliminar las funciones temporales
DROP FUNCTION IF EXISTS generate_unique_slug(TEXT);
DROP FUNCTION IF EXISTS generate_base_slug(TEXT); 