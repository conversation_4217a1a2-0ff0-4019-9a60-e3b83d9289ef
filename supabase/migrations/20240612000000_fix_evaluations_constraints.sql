-- Script para corregir las restricciones de la tabla evaluations

-- Eliminar todas las restricciones únicas existentes en la tabla evaluations
DO $$
DECLARE
    constraint_name text;
BEGIN
    FOR constraint_name IN
        SELECT conname
        FROM pg_constraint
        WHERE conrelid = 'public.evaluations'::regclass
        AND contype = 'u'  -- 'u' significa restricción única
    LOOP
        EXECUTE 'ALTER TABLE public.evaluations DROP CONSTRAINT IF EXISTS ' || constraint_name;
    END LOOP;
END $$;

-- Crear una nueva restricción única con el nombre correcto
ALTER TABLE public.evaluations
ADD CONSTRAINT evaluations_student_indicator_subject_homework_unique
UNIQUE (student_id, indicator_id, subject_id, homework_id);