-- Verificar si la tabla evaluation_levels existe
DO $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'evaluation_levels'
  ) THEN
    -- Insertar los niveles por defecto solo si no existen
    -- Nivel 1
    IF NOT EXISTS (SELECT 1 FROM public.evaluation_levels WHERE level_number = 1) THEN
      INSERT INTO public.evaluation_levels (level_number, title) VALUES (1, 'Nivel básico');
    END IF;
    
    -- Nivel 2
    IF NOT EXISTS (SELECT 1 FROM public.evaluation_levels WHERE level_number = 2) THEN
      INSERT INTO public.evaluation_levels (level_number, title) VALUES (2, 'Nivel intermedio');
    END IF;
    
    -- Nivel 3
    IF NOT EXISTS (SELECT 1 FROM public.evaluation_levels WHERE level_number = 3) THEN
      INSERT INTO public.evaluation_levels (level_number, title) VALUES (3, 'Nivel avanzado');
    END IF;
    
    -- Nivel 4
    IF NOT EXISTS (SELECT 1 FROM public.evaluation_levels WHERE level_number = 4) THEN
      INSERT INTO public.evaluation_levels (level_number, title) VALUES (4, 'Nivel experto');
    END IF;
  END IF;
END $$;
