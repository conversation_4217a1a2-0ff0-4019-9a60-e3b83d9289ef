-- Migración para refactorizar el sistema de evaluaciones
-- Esta migración simplifica la estructura de evaluaciones para permitir que un profesor
-- pueda evaluar a todos los alumnos de un curso para un trabajo concreto,
-- incluyendo las descripciones de los indicadores y sus valores numéricos.

-- Primero, creamos una tabla temporal para almacenar las evaluaciones existentes
-- que queremos preservar durante la migración
CREATE TEMP TABLE temp_evaluations AS
SELECT
  e.id,
  e.homework_id,
  e.student_id,
  e.indicator_id,
  e.level_id,
  i.criteria_id,
  e.user_id,
  COALESCE(e.level_number, il.level_number) AS level_number,
  COALESCE(e.level_description, ild.description) AS level_description,
  h.subject_id,
  s.course_id,
  e.created_at,
  e.updated_at
FROM
  evaluations e
  JOIN indicators i ON e.indicator_id = i.id
  JOIN homeworks h ON e.homework_id = h.id
  JOIN subjects s ON h.subject_id = s.id
  LEFT JOIN indicator_levels il ON e.level_id = il.id
  LEFT JOIN indicator_level_descriptions ild ON il.id = ild.level_id AND i.id = ild.indicator_id;

-- Eliminar las tablas existentes que vamos a reemplazar
DROP TABLE IF EXISTS criteria_evaluation_summaries;

-- Crear una nueva tabla para los niveles de evaluación
-- Esta tabla contendrá los 4 niveles posibles (1-4) con sus descripciones genéricas
CREATE TABLE IF NOT EXISTS public.evaluation_levels (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  level_number INTEGER NOT NULL CHECK (level_number BETWEEN 1 AND 4),
  title TEXT NOT NULL,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(level_number)
);

-- Insertar los niveles estándar si la tabla está vacía
INSERT INTO public.evaluation_levels (level_number, title)
SELECT t.level_number, t.title
FROM (
  VALUES
    (1, 'Nivel 1'),
    (2, 'Nivel 2'),
    (3, 'Nivel 3'),
    (4, 'Nivel 4')
) AS t(level_number, title)
WHERE NOT EXISTS (SELECT 1 FROM public.evaluation_levels);

-- Crear una tabla para las descripciones específicas de cada nivel por indicador
CREATE TABLE IF NOT EXISTS public.indicator_level_descriptions (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  indicator_id uuid NOT NULL REFERENCES public.indicators(id) ON DELETE CASCADE,
  level_id uuid NOT NULL REFERENCES public.evaluation_levels(id) ON DELETE CASCADE,
  description TEXT NOT NULL,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(indicator_id, level_id)
);

-- Eliminar la tabla de evaluaciones existente
DROP TABLE IF EXISTS public.evaluations;

-- Crear una nueva tabla de evaluaciones optimizada
CREATE TABLE IF NOT EXISTS public.evaluations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  homework_id UUID NOT NULL REFERENCES public.homeworks(id) ON DELETE CASCADE,
  student_id UUID NOT NULL REFERENCES public.students(id) ON DELETE CASCADE,
  indicator_id UUID NOT NULL REFERENCES public.indicators(id) ON DELETE CASCADE,
  level_id UUID NOT NULL REFERENCES public.evaluation_levels(id) ON DELETE CASCADE,
  level_number INTEGER NOT NULL CHECK (level_number BETWEEN 1 AND 4),
  level_description TEXT NOT NULL,
  course_id UUID NOT NULL REFERENCES public.courses(id) ON DELETE CASCADE,
  subject_id UUID NOT NULL REFERENCES public.subjects(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  -- Restricción única para evitar evaluaciones duplicadas
  CONSTRAINT evaluations_student_indicator_homework_unique UNIQUE (student_id, indicator_id, homework_id)
);

-- Habilitar RLS
ALTER TABLE public.evaluations ENABLE ROW LEVEL SECURITY;

-- Crear políticas de seguridad
CREATE POLICY "Los usuarios pueden ver sus evaluaciones"
  ON public.evaluations FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Los usuarios pueden crear evaluaciones"
  ON public.evaluations FOR INSERT
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Los usuarios pueden actualizar sus evaluaciones"
  ON public.evaluations FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Los usuarios pueden eliminar sus evaluaciones"
  ON public.evaluations FOR DELETE
  USING (user_id = auth.uid());

-- Crear índices para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS evaluations_homework_id_idx ON public.evaluations(homework_id);
CREATE INDEX IF NOT EXISTS evaluations_student_id_idx ON public.evaluations(student_id);
CREATE INDEX IF NOT EXISTS evaluations_indicator_id_idx ON public.evaluations(indicator_id);
CREATE INDEX IF NOT EXISTS evaluations_course_id_idx ON public.evaluations(course_id);
CREATE INDEX IF NOT EXISTS evaluations_subject_id_idx ON public.evaluations(subject_id);
CREATE INDEX IF NOT EXISTS evaluations_user_id_idx ON public.evaluations(user_id);
CREATE INDEX IF NOT EXISTS evaluations_level_id_idx ON public.evaluations(level_id);

-- Verificar y optimizar la tabla homework_criteria
ALTER TABLE public.homework_criteria DROP CONSTRAINT IF EXISTS unique_homework_criteria;
ALTER TABLE public.homework_criteria ADD CONSTRAINT unique_homework_criteria UNIQUE (homework_id, criteria_id);

-- Asegurar que los índices existan
CREATE INDEX IF NOT EXISTS idx_homework_criteria_homework_id ON public.homework_criteria(homework_id);
CREATE INDEX IF NOT EXISTS idx_homework_criteria_criteria_id ON public.homework_criteria(criteria_id);

-- Habilitar RLS para homework_criteria
ALTER TABLE public.homework_criteria ENABLE ROW LEVEL SECURITY;

-- Crear políticas de seguridad para homework_criteria
CREATE POLICY "Los usuarios pueden ver las relaciones homework_criteria"
  ON public.homework_criteria FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM homeworks h
      JOIN subjects s ON h.subject_id = s.id
      WHERE h.id = homework_id AND s.user_id = auth.uid()
    )
  );

CREATE POLICY "Los usuarios pueden crear relaciones homework_criteria"
  ON public.homework_criteria FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM homeworks h
      JOIN subjects s ON h.subject_id = s.id
      WHERE h.id = homework_id AND s.user_id = auth.uid()
    )
  );

CREATE POLICY "Los usuarios pueden actualizar relaciones homework_criteria"
  ON public.homework_criteria FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM homeworks h
      JOIN subjects s ON h.subject_id = s.id
      WHERE h.id = homework_id AND s.user_id = auth.uid()
    )
  );

CREATE POLICY "Los usuarios pueden eliminar relaciones homework_criteria"
  ON public.homework_criteria FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM homeworks h
      JOIN subjects s ON h.subject_id = s.id
      WHERE h.id = homework_id AND s.user_id = auth.uid()
    )
  );

-- Migrar los datos de la tabla temporal a la nueva estructura
INSERT INTO public.evaluations (
  homework_id,
  student_id,
  indicator_id,
  level_id,
  level_number,
  level_description,
  course_id,
  subject_id,
  user_id,
  created_at,
  updated_at
)
SELECT
  te.homework_id,
  te.student_id,
  te.indicator_id,
  el.id AS level_id,
  te.level_number,
  te.level_description,
  te.course_id,
  te.subject_id,
  te.user_id,
  te.created_at,
  te.updated_at
FROM
  temp_evaluations te
  JOIN evaluation_levels el ON te.level_number = el.level_number
ON CONFLICT (student_id, indicator_id, homework_id)
DO UPDATE SET
  level_id = EXCLUDED.level_id,
  level_number = EXCLUDED.level_number,
  level_description = EXCLUDED.level_description,
  updated_at = EXCLUDED.updated_at;

-- Crear una función para actualizar automáticamente level_number y level_description
CREATE OR REPLACE FUNCTION update_evaluation_level_info()
RETURNS TRIGGER AS $$
BEGIN
  -- Actualizar level_number basado en level_id
  SELECT level_number INTO NEW.level_number
  FROM evaluation_levels
  WHERE id = NEW.level_id;

  -- Buscar una descripción específica para este indicador y nivel
  SELECT description INTO NEW.level_description
  FROM indicator_level_descriptions
  WHERE indicator_id = NEW.indicator_id AND level_id = NEW.level_id;

  -- Si no hay descripción específica, usar la descripción genérica del nivel
  IF NEW.level_description IS NULL THEN
    SELECT title INTO NEW.level_description
    FROM evaluation_levels
    WHERE id = NEW.level_id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Crear un trigger para actualizar automáticamente la información del nivel
CREATE TRIGGER update_evaluation_level_info_trigger
BEFORE INSERT OR UPDATE ON evaluations
FOR EACH ROW
EXECUTE FUNCTION update_evaluation_level_info();

-- Crear una vista para facilitar la consulta de evaluaciones por trabajo y estudiante
CREATE OR REPLACE VIEW homework_student_evaluations AS
SELECT
  e.id,
  e.homework_id,
  h.title AS homework_title,
  h.slug AS homework_slug,
  e.student_id,
  s.first_name || ' ' || s.last_name1 || COALESCE(' ' || s.last_name2, '') AS student_name,
  s.slug AS student_slug,
  e.indicator_id,
  i.title AS indicator_title,
  i.slug AS indicator_slug,
  c.id AS criteria_id,
  c.name AS criteria_name,
  c.slug AS criteria_slug,
  e.level_number,
  e.level_description,
  e.course_id,
  co.title AS course_title,
  co.slug AS course_slug,
  e.subject_id,
  su.name AS subject_name,
  su.slug AS subject_slug,
  e.user_id,
  e.created_at,
  e.updated_at
FROM
  evaluations e
  JOIN homeworks h ON e.homework_id = h.id
  JOIN students s ON e.student_id = s.id
  JOIN indicators i ON e.indicator_id = i.id
  JOIN criterias c ON i.criteria_id = c.id
  JOIN courses co ON e.course_id = co.id
  JOIN subjects su ON e.subject_id = su.id;

-- Habilitar RLS para las tablas relacionadas
ALTER TABLE public.evaluation_levels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.indicator_level_descriptions ENABLE ROW LEVEL SECURITY;

-- Crear políticas de seguridad para evaluation_levels
CREATE POLICY "Todos pueden ver los niveles de evaluación"
  ON public.evaluation_levels FOR SELECT
  TO authenticated
  USING (true);

-- Crear políticas de seguridad para indicator_level_descriptions
CREATE POLICY "Los usuarios pueden ver las descripciones de niveles de indicadores"
  ON public.indicator_level_descriptions FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM indicators i
      JOIN criterias c ON i.criteria_id = c.id
      WHERE i.id = indicator_id AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Los usuarios pueden crear descripciones de niveles de indicadores"
  ON public.indicator_level_descriptions FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM indicators i
      JOIN criterias c ON i.criteria_id = c.id
      WHERE i.id = indicator_id AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Los usuarios pueden actualizar descripciones de niveles de indicadores"
  ON public.indicator_level_descriptions FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM indicators i
      JOIN criterias c ON i.criteria_id = c.id
      WHERE i.id = indicator_id AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Los usuarios pueden eliminar descripciones de niveles de indicadores"
  ON public.indicator_level_descriptions FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM indicators i
      JOIN criterias c ON i.criteria_id = c.id
      WHERE i.id = indicator_id AND c.user_id = auth.uid()
    )
  );

-- Eliminar la tabla temporal
DROP TABLE temp_evaluations;
