-- Eliminar la tabla de evaluaciones anterior si existe
DROP TABLE IF EXISTS public.evaluations;

-- Crear la nueva tabla de evaluaciones centrada en trabajos (homeworks)
CREATE TABLE IF NOT EXISTS public.evaluations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  homework_id UUID NOT NULL REFERENCES public.homeworks(id) ON DELETE CASCADE,
  student_id UUID NOT NULL REFERENCES public.students(id) ON DELETE CASCADE,
  indicator_id UUID NOT NULL REFERENCES public.indicators(id) ON DELETE CASCADE,
  level_id UUID NOT NULL REFERENCES public.indicator_levels(id) ON DELETE CASCADE,
  course_id UUID NOT NULL REFERENCES public.courses(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  -- Restricción única para evitar evaluaciones duplicadas
  UNIQUE(homework_id, student_id, indicator_id)
);

-- Habilitar RLS
ALTER TABLE public.evaluations ENABLE ROW LEVEL SECURITY;

-- Crear políticas de seguridad
DO $$
BEGIN
  -- Política para ver evaluaciones
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE tablename = 'evaluations' AND policyname = 'Los usuarios pueden ver sus evaluaciones'
  ) THEN
    CREATE POLICY "Los usuarios pueden ver sus evaluaciones"
      ON public.evaluations FOR SELECT
      USING (user_id = auth.uid());
  END IF;

  -- Política para insertar evaluaciones
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE tablename = 'evaluations' AND policyname = 'Los usuarios pueden crear evaluaciones'
  ) THEN
    CREATE POLICY "Los usuarios pueden crear evaluaciones"
      ON public.evaluations FOR INSERT
      WITH CHECK (user_id = auth.uid());
  END IF;

  -- Política para actualizar evaluaciones
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE tablename = 'evaluations' AND policyname = 'Los usuarios pueden actualizar sus evaluaciones'
  ) THEN
    CREATE POLICY "Los usuarios pueden actualizar sus evaluaciones"
      ON public.evaluations FOR UPDATE
      USING (user_id = auth.uid());
  END IF;

  -- Política para eliminar evaluaciones
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE tablename = 'evaluations' AND policyname = 'Los usuarios pueden eliminar sus evaluaciones'
  ) THEN
    CREATE POLICY "Los usuarios pueden eliminar sus evaluaciones"
      ON public.evaluations FOR DELETE
      USING (user_id = auth.uid());
  END IF;
END $$;

-- Crear índices para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS evaluations_homework_id_idx ON public.evaluations(homework_id);
CREATE INDEX IF NOT EXISTS evaluations_student_id_idx ON public.evaluations(student_id);
CREATE INDEX IF NOT EXISTS evaluations_indicator_id_idx ON public.evaluations(indicator_id);
CREATE INDEX IF NOT EXISTS evaluations_course_id_idx ON public.evaluations(course_id);
CREATE INDEX IF NOT EXISTS evaluations_user_id_idx ON public.evaluations(user_id);
