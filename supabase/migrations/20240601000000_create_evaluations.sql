-- Crear tabla de evaluaciones
create table if not exists public.evaluations (
  id uuid default gen_random_uuid() primary key,
  student_id uuid not null references public.students(id) on delete cascade,
  indicator_id uuid not null references public.indicators(id) on delete cascade,
  level_id uuid not null references public.indicator_levels(id) on delete cascade,
  subject_id uuid not null references public.subjects(id) on delete cascade,
  course_id uuid not null references public.courses(id) on delete cascade,
  homework_id uuid not null references public.homeworks(id) on delete cascade,
  user_id uuid not null references auth.users(id) on delete cascade,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  -- Restricción única para evitar evaluaciones duplicadas
  unique(student_id, indicator_id, subject_id, homework_id)
);

-- Habilitar RLS
alter table public.evaluations enable row level security;

-- Crear políticas de seguridad
do $$
begin
  -- Política para ver evaluaciones
  if not exists (
    select 1 from pg_policies where tablename = 'evaluations' and policyname = 'Los usuarios pueden ver sus evaluaciones'
  ) then
    create policy "Los usuarios pueden ver sus evaluaciones"
      on public.evaluations for select
      using (user_id = auth.uid());
  end if;

  -- Política para insertar evaluaciones
  if not exists (
    select 1 from pg_policies where tablename = 'evaluations' and policyname = 'Los usuarios pueden crear evaluaciones'
  ) then
    create policy "Los usuarios pueden crear evaluaciones"
      on public.evaluations for insert
      with check (user_id = auth.uid());
  end if;

  -- Política para actualizar evaluaciones
  if not exists (
    select 1 from pg_policies where tablename = 'evaluations' and policyname = 'Los usuarios pueden actualizar sus evaluaciones'
  ) then
    create policy "Los usuarios pueden actualizar sus evaluaciones"
      on public.evaluations for update
      using (user_id = auth.uid());
  end if;

  -- Política para eliminar evaluaciones
  if not exists (
    select 1 from pg_policies where tablename = 'evaluations' and policyname = 'Los usuarios pueden eliminar sus evaluaciones'
  ) then
    create policy "Los usuarios pueden eliminar sus evaluaciones"
      on public.evaluations for delete
      using (user_id = auth.uid());
  end if;
end $$;

-- Crear función para actualizar updated_at si no existe
create or replace function public.handle_updated_at()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

-- Crear trigger para actualizar updated_at
do $$
begin
  if not exists (
    select 1 from pg_trigger where tgname = 'handle_evaluations_updated_at'
  ) then
    create trigger handle_evaluations_updated_at
      before update on public.evaluations
      for each row
      execute function public.handle_updated_at();
  end if;
end $$;

-- Crear índices para mejorar el rendimiento
create index if not exists evaluations_student_id_idx on public.evaluations(student_id);
create index if not exists evaluations_indicator_id_idx on public.evaluations(indicator_id);
create index if not exists evaluations_subject_id_idx on public.evaluations(subject_id);
create index if not exists evaluations_course_id_idx on public.evaluations(course_id);
create index if not exists evaluations_user_id_idx on public.evaluations(user_id);