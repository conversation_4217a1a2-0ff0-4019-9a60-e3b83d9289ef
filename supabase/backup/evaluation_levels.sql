create table public.evaluation_levels (
  id uuid not null default gen_random_uuid (),
  level_number integer not null,
  title text not null,
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  updated_at timestamp with time zone not null default timezone ('utc'::text, now()),
  constraint evaluation_levels_pkey primary key (id),
  constraint evaluation_levels_level_number_key unique (level_number),
  constraint evaluation_levels_level_number_check check (
    (
      (level_number >= 1)
      and (level_number <= 4)
    )
  )
) TABLESPACE pg_default;