create table public.indicator_level_descriptions (
  id uuid not null default gen_random_uuid (),
  indicator_id uuid not null,
  level_id uuid not null,
  description text not null,
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  updated_at timestamp with time zone not null default timezone ('utc'::text, now()),
  constraint indicator_level_descriptions_pkey primary key (id),
  constraint indicator_level_descriptions_indicator_id_level_id_key unique (indicator_id, level_id),
  constraint indicator_level_descriptions_indicator_id_fkey foreign KEY (indicator_id) references indicators (id) on delete CASCADE,
  constraint indicator_level_descriptions_level_id_fkey foreign KEY (level_id) references evaluation_levels (id) on delete CASCADE
) TABLESPACE pg_default;