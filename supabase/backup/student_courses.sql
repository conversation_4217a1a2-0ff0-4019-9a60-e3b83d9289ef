create table public.student_courses (
  student_id uuid not null,
  course_id uuid not null,
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  constraint student_courses_pkey primary key (student_id, course_id),
  constraint student_courses_course_id_fkey foreign KEY (course_id) references courses (id) on delete CASCADE,
  constraint student_courses_student_id_fkey foreign KEY (student_id) references students (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists student_courses_student_id_idx on public.student_courses using btree (student_id) TABLESPACE pg_default;

create index IF not exists student_courses_course_id_idx on public.student_courses using btree (course_id) TABLESPACE pg_default;

create index IF not exists idx_student_courses_student_id on public.student_courses using btree (student_id) TABLESPACE pg_default;

create index IF not exists idx_student_courses_course_id on public.student_courses using btree (course_id) TABLESPACE pg_default;