create table public.evaluations (
  id uuid not null default gen_random_uuid (),
  homework_id uuid not null,
  student_id uuid not null,
  indicator_id uuid not null,
  level_id uuid not null,
  user_id uuid not null,
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  updated_at timestamp with time zone not null default timezone ('utc'::text, now()),
  level_number integer null,
  level_description text null,
  constraint evaluations_pkey primary key (id),
  constraint evaluations_homework_student_indicator_unique unique (homework_id, student_id, indicator_id),
  constraint evaluations_homework_id_fkey foreign KEY (homework_id) references homeworks (id) on delete CASCADE,
  constraint evaluations_student_id_fkey foreign KEY (student_id) references students (id) on delete CASCADE,
  constraint evaluations_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE,
  constraint evaluations_level_id_fkey foreign KEY (level_id) references indicator_levels (id) on delete CASCADE,
  constraint evaluations_indicator_id_fkey foreign KEY (indicator_id) references indicators (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists evaluations_homework_id_idx on public.evaluations using btree (homework_id) TABLESPACE pg_default;

create index IF not exists evaluations_student_id_idx on public.evaluations using btree (student_id) TABLESPACE pg_default;

create index IF not exists evaluations_indicator_id_idx on public.evaluations using btree (indicator_id) TABLESPACE pg_default;

create index IF not exists evaluations_user_id_idx on public.evaluations using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_evaluations_student_indicator on public.evaluations using btree (student_id, indicator_id) TABLESPACE pg_default;

create trigger handle_evaluations_updated_at BEFORE
update on evaluations for EACH row
execute FUNCTION handle_updated_at ();

create trigger update_criteria_summary_after_evaluation
after INSERT
or
update on evaluations for EACH row
execute FUNCTION update_criteria_evaluation_summary ();