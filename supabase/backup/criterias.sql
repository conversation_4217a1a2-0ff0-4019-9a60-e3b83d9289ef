create table public.criterias (
  id uuid not null default gen_random_uuid (),
  name text not null,
  description text null,
  slug text not null,
  user_id uuid not null,
  subject_id uuid not null,
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  updated_at timestamp with time zone not null default timezone ('utc'::text, now()),
  constraint criterias_pkey primary key (id),
  constraint criterias_subject_id_fkey foreign KEY (subject_id) references subjects (id) on delete CASCADE,
  constraint criterias_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists criterias_name_search_idx on public.criterias using gin (name gin_trgm_ops) TABLESPACE pg_default;

create trigger generate_unique_criteria_slug_trigger BEFORE INSERT on criterias for EACH row
execute FUNCTION generate_unique_criteria_slug ();

create trigger update_criterias_updated_at BEFORE
update on criterias for EACH row
execute FUNCTION update_updated_at_column ();