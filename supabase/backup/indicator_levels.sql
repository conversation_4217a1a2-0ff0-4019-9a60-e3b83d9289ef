create table public.indicator_levels (
  id uuid not null default gen_random_uuid (),
  indicator_id uuid not null,
  level_number integer not null,
  title text not null,
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  updated_at timestamp with time zone not null default timezone ('utc'::text, now()),
  constraint indicator_levels_pkey primary key (id),
  constraint indicator_levels_indicator_id_level_number_key unique (indicator_id, level_number),
  constraint indicator_levels_indicator_id_fkey foreign KEY (indicator_id) references indicators (id) on delete CASCADE,
  constraint indicator_levels_level_number_check check (
    (
      (level_number >= 1)
      and (level_number <= 4)
    )
  )
) TABLESPACE pg_default;