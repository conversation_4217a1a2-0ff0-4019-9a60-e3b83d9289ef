create table public.subjects (
  id uuid not null default gen_random_uuid (),
  name text not null,
  course_id uuid null,
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  updated_at timestamp with time zone not null default timezone ('utc'::text, now()),
  user_id uuid null,
  slug text null,
  description text null,
  constraint subjects_pkey primary key (id),
  constraint subjects_user_id_name_key unique (user_id, name),
  constraint subjects_course_id_fkey foreign KEY (course_id) references courses (id) on delete CASCADE,
  constraint subjects_user_id_fkey foreign KEY (user_id) references auth.users (id)
) TABLESPACE pg_default;

create trigger generate_unique_slug_trigger BEFORE INSERT on subjects for EACH row
execute FUNCTION generate_unique_slug ();

create trigger generate_unique_subject_slug_trigger BEFORE INSERT on subjects for EACH row
execute FUNCTION generate_unique_subject_slug ();