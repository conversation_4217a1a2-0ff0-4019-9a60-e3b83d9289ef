create table public.courses (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  title text not null,
  description text null,
  created_at timestamp with time zone null default now(),
  slug text null,
  constraint courses_pkey primary key (id),
  constraint courses_user_id_title_key unique (user_id, title),
  constraint courses_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;

create trigger generate_unique_course_slug_trigger BEFORE INSERT on courses for EACH row
execute FUNCTION generate_unique_course_slug ();