create table public.indicators (
  id uuid not null default gen_random_uuid (),
  title text not null,
  criteria_id uuid not null,
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  updated_at timestamp with time zone not null default timezone ('utc'::text, now()),
  slug text not null,
  constraint indicators_pkey primary key (id),
  constraint indicators_criteria_id_fkey foreign KEY (criteria_id) references criterias (id) on delete CASCADE
) TABLESPACE pg_default;

create unique INDEX IF not exists indicators_slug_idx on public.indicators using btree (slug) TABLESPACE pg_default;

create trigger handle_indicators_updated_at BEFORE
update on indicators for EACH row
execute FUNCTION handle_updated_at ();