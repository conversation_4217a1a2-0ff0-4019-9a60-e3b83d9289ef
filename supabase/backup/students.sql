create table public.students (
  id uuid not null default extensions.uuid_generate_v4 (),
  first_name character varying(255) not null,
  last_name1 character varying(255) not null,
  last_name2 character varying(255) null,
  is_pi boolean null default false,
  slug character varying(255) not null,
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  updated_at timestamp with time zone not null default timezone ('utc'::text, now()),
  constraint students_pkey primary key (id)
) TABLESPACE pg_default;

create index IF not exists idx_students_slug on public.students using btree (slug) TABLESPACE pg_default;

create trigger generate_unique_student_slug_trigger BEFORE INSERT on students for EACH row
execute FUNCTION generate_unique_student_slug ();

create trigger set_student_slug BEFORE INSERT
or
update on students for EACH row
execute FUNCTION generate_student_slug ();