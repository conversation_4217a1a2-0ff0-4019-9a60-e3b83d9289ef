create table public.homeworks (
  id uuid not null default gen_random_uuid (),
  title text not null,
  description text null,
  subject_id uuid not null,
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  updated_at timestamp with time zone not null default timezone ('utc'::text, now()),
  slug text null,
  constraint homeworks_pkey primary key (id),
  constraint homeworks_subject_id_title_key unique (subject_id, title),
  constraint homeworks_subject_id_fkey foreign KEY (subject_id) references subjects (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists homeworks_slug_idx on public.homeworks using btree (slug) TABLESPACE pg_default;

create index IF not exists homeworks_subject_id_idx on public.homeworks using btree (subject_id) TABLESPACE pg_default;

create trigger handle_homeworks_updated_at BEFORE
update on homeworks for EACH row
execute FUNCTION handle_updated_at ();