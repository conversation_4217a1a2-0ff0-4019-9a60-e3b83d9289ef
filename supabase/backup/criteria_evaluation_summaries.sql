create table public.criteria_evaluation_summaries (
  id uuid not null default gen_random_uuid (),
  student_id uuid not null,
  criteria_id uuid not null,
  average_level numeric(3, 2) not null,
  level_description text not null,
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  updated_at timestamp with time zone not null default timezone ('utc'::text, now()),
  constraint criteria_evaluation_summaries_pkey primary key (id),
  constraint criteria_evaluation_summaries_student_id_criteria_id_key unique (student_id, criteria_id),
  constraint criteria_evaluation_summaries_criteria_id_fkey foreign KEY (criteria_id) references criterias (id) on delete CASCADE,
  constraint criteria_evaluation_summaries_student_id_fkey foreign KEY (student_id) references students (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_criteria_summaries_student on public.criteria_evaluation_summaries using btree (student_id) TABLESPACE pg_default;