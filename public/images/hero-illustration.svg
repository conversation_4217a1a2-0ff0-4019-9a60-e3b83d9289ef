<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Education Evaluation Illustration</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#1D88AF" offset="0%"></stop>
            <stop stop-color="#38BDF8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#E0F2FE" offset="0%"></stop>
            <stop stop-color="#BAE6FD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-3">
            <stop stop-color="#F0F9FF" offset="0%"></stop>
            <stop stop-color="#E0F2FE" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background Elements -->
        <circle id="Background-Circle-1" fill="url(#linearGradient-2)" cx="400" cy="300" r="250" opacity="0.5"></circle>
        <circle id="Background-Circle-2" fill="url(#linearGradient-3)" cx="200" cy="150" r="100" opacity="0.7"></circle>
        <circle id="Background-Circle-3" fill="url(#linearGradient-3)" cx="600" cy="450" r="120" opacity="0.6"></circle>
        
        <!-- Main Document -->
        <rect id="Document-Background" fill="#FFFFFF" x="250" y="100" width="300" height="400" rx="10"></rect>
        <rect id="Document-Header" fill="url(#linearGradient-1)" x="250" y="100" width="300" height="60" rx="10 10 0 0"></rect>
        <text id="Document-Title" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#FFFFFF">
            <tspan x="320" y="140">Evaluación</tspan>
        </text>
        
        <!-- Document Content -->
        <rect id="Content-Line-1" fill="#E0E0E0" x="280" y="180" width="240" height="10" rx="5"></rect>
        <rect id="Content-Line-2" fill="#E0E0E0" x="280" y="200" width="180" height="10" rx="5"></rect>
        
        <!-- Evaluation Criteria -->
        <rect id="Criteria-Box-1" fill="#F8FAFC" x="280" y="240" width="240" height="60" rx="5"></rect>
        <rect id="Criteria-Box-2" fill="#F8FAFC" x="280" y="320" width="240" height="60" rx="5"></rect>
        <rect id="Criteria-Box-3" fill="#F8FAFC" x="280" y="400" width="240" height="60" rx="5"></rect>
        
        <!-- Evaluation Indicators -->
        <circle id="Indicator-1-1" fill="#38BDF8" cx="300" cy="260" r="8"></circle>
        <circle id="Indicator-1-2" fill="#38BDF8" cx="330" cy="260" r="8"></circle>
        <circle id="Indicator-1-3" fill="#E0E0E0" cx="360" cy="260" r="8"></circle>
        <circle id="Indicator-1-4" fill="#E0E0E0" cx="390" cy="260" r="8"></circle>
        
        <circle id="Indicator-2-1" fill="#38BDF8" cx="300" cy="340" r="8"></circle>
        <circle id="Indicator-2-2" fill="#38BDF8" cx="330" cy="340" r="8"></circle>
        <circle id="Indicator-2-3" fill="#38BDF8" cx="360" cy="340" r="8"></circle>
        <circle id="Indicator-2-4" fill="#E0E0E0" cx="390" cy="340" r="8"></circle>
        
        <circle id="Indicator-3-1" fill="#38BDF8" cx="300" cy="420" r="8"></circle>
        <circle id="Indicator-3-2" fill="#E0E0E0" cx="330" cy="420" r="8"></circle>
        <circle id="Indicator-3-3" fill="#E0E0E0" cx="360" cy="420" r="8"></circle>
        <circle id="Indicator-3-4" fill="#E0E0E0" cx="390" cy="420" r="8"></circle>
        
        <!-- Criteria Text -->
        <rect id="Criteria-Text-1-1" fill="#E0E0E0" x="420" y="250" width="80" height="8" rx="4"></rect>
        <rect id="Criteria-Text-1-2" fill="#E0E0E0" x="420" y="270" width="60" height="8" rx="4"></rect>
        
        <rect id="Criteria-Text-2-1" fill="#E0E0E0" x="420" y="330" width="80" height="8" rx="4"></rect>
        <rect id="Criteria-Text-2-2" fill="#E0E0E0" x="420" y="350" width="60" height="8" rx="4"></rect>
        
        <rect id="Criteria-Text-3-1" fill="#E0E0E0" x="420" y="410" width="80" height="8" rx="4"></rect>
        <rect id="Criteria-Text-3-2" fill="#E0E0E0" x="420" y="430" width="60" height="8" rx="4"></rect>
        
        <!-- Floating Elements -->
        <g id="Floating-Chart" transform="translate(100, 200)">
            <rect fill="#FFFFFF" x="0" y="0" width="120" height="120" rx="10"></rect>
            <rect fill="url(#linearGradient-1)" x="20" y="20" width="30" height="80" rx="4"></rect>
            <rect fill="#BAE6FD" x="60" y="40" width="30" height="60" rx="4"></rect>
        </g>
        
        <g id="Floating-Badge" transform="translate(580, 180)">
            <circle fill="url(#linearGradient-1)" cx="40" cy="40" r="40"></circle>
            <text font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF">
                <tspan x="30" y="48">A</tspan>
            </text>
        </g>
        
        <g id="Floating-Checkmark" transform="translate(550, 300)">
            <circle fill="#4CAF50" cx="30" cy="30" r="30"></circle>
            <path d="M20,30 L27,37 L40,23" stroke="#FFFFFF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
        </g>
    </g>
</svg>
