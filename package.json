{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/icons-material": "^6.4.8", "@mui/material": "^5.12.1", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.1", "next": "15.2.1", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "supabase": "^2.15.8", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "eslint": "^9.24.0", "typescript": "^5"}}